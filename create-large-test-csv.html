<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Large Test CSV</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .progress {
            margin-top: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #27ae60;
            width: 0%;
            transition: width 0.3s ease;
        }
        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #7f8c8d;
        }
        .info {
            background-color: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Large Test CSV Generator</h1>
        
        <div class="info">
            <strong>Purpose:</strong> Generate large CSV files to test the KYC analysis tool's performance with big datasets.
        </div>
        
        <div class="warning">
            <strong>Warning:</strong> Large files (1M+ rows) may take several minutes to generate and download.
        </div>
        
        <div class="form-group">
            <label for="rowCount">Number of Rows:</label>
            <select id="rowCount">
                <option value="1000">1,000 rows (~100KB)</option>
                <option value="10000">10,000 rows (~1MB)</option>
                <option value="100000">100,000 rows (~10MB)</option>
                <option value="500000">500,000 rows (~50MB)</option>
                <option value="1000000" selected>1,000,000 rows (~100MB)</option>
                <option value="2000000">2,000,000 rows (~200MB)</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="missingDataPercent">Missing Data Percentage:</label>
            <select id="missingDataPercent">
                <option value="5">5% missing data</option>
                <option value="10">10% missing data</option>
                <option value="15" selected>15% missing data</option>
                <option value="20">20% missing data</option>
                <option value="30">30% missing data</option>
            </select>
        </div>
        
        <button onclick="generateCSV()" id="generateBtn">Generate Large Test CSV</button>
        
        <div class="progress" id="progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Generating...</div>
        </div>
    </div>

    <script>
        // KYC field names
        const kycFields = [
            'Customer_ID', 'Full_Name', 'Date_of_Birth', 'Nationality', 'ID_Number',
            'ID_Type', 'Address', 'City', 'Country', 'Postal_Code',
            'Phone_Number', 'Email', 'Occupation', 'Employer', 'Annual_Income',
            'Source_of_Funds', 'Bank_Account', 'Risk_Rating', 'PEP_Status', 'Sanctions_Check'
        ];

        function generateRandomData(missingPercent) {
            const data = {};
            
            // Generate data for each field
            kycFields.forEach(field => {
                // Randomly decide if this field should be missing
                if (Math.random() * 100 < missingPercent) {
                    data[field] = '';
                    return;
                }
                
                // Generate realistic data based on field type
                switch(field) {
                    case 'Customer_ID':
                        data[field] = 'CUST' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
                        break;
                    case 'Full_Name':
                        const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily'];
                        const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
                        data[field] = firstNames[Math.floor(Math.random() * firstNames.length)] + ' ' + 
                                     lastNames[Math.floor(Math.random() * lastNames.length)];
                        break;
                    case 'Date_of_Birth':
                        const year = 1950 + Math.floor(Math.random() * 50);
                        const month = (1 + Math.floor(Math.random() * 12)).toString().padStart(2, '0');
                        const day = (1 + Math.floor(Math.random() * 28)).toString().padStart(2, '0');
                        data[field] = `${year}-${month}-${day}`;
                        break;
                    case 'Nationality':
                        const countries = ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'JP', 'SG'];
                        data[field] = countries[Math.floor(Math.random() * countries.length)];
                        break;
                    case 'ID_Number':
                        data[field] = Math.floor(Math.random() * **********).toString();
                        break;
                    case 'ID_Type':
                        const idTypes = ['Passport', 'Driver License', 'National ID', 'SSN'];
                        data[field] = idTypes[Math.floor(Math.random() * idTypes.length)];
                        break;
                    case 'Address':
                        data[field] = Math.floor(Math.random() * 9999) + ' Main Street';
                        break;
                    case 'City':
                        const cities = ['New York', 'London', 'Toronto', 'Sydney', 'Berlin', 'Paris', 'Tokyo', 'Singapore'];
                        data[field] = cities[Math.floor(Math.random() * cities.length)];
                        break;
                    case 'Country':
                        const fullCountries = ['United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'Singapore'];
                        data[field] = fullCountries[Math.floor(Math.random() * fullCountries.length)];
                        break;
                    case 'Postal_Code':
                        data[field] = Math.floor(Math.random() * 99999).toString().padStart(5, '0');
                        break;
                    case 'Phone_Number':
                        data[field] = '+1-' + Math.floor(Math.random() * 900 + 100) + '-' + Math.floor(Math.random() * 900 + 100) + '-' + Math.floor(Math.random() * 9000 + 1000);
                        break;
                    case 'Email':
                        const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'company.com'];
                        data[field] = 'user' + Math.floor(Math.random() * 10000) + '@' + domains[Math.floor(Math.random() * domains.length)];
                        break;
                    case 'Occupation':
                        const occupations = ['Engineer', 'Teacher', 'Doctor', 'Lawyer', 'Manager', 'Consultant', 'Analyst', 'Developer'];
                        data[field] = occupations[Math.floor(Math.random() * occupations.length)];
                        break;
                    case 'Employer':
                        const employers = ['Tech Corp', 'Global Inc', 'Finance Ltd', 'Consulting Group', 'Healthcare Systems', 'Education Board'];
                        data[field] = employers[Math.floor(Math.random() * employers.length)];
                        break;
                    case 'Annual_Income':
                        data[field] = (30000 + Math.floor(Math.random() * 200000)).toString();
                        break;
                    case 'Source_of_Funds':
                        const sources = ['Salary', 'Business Income', 'Investment', 'Inheritance', 'Savings'];
                        data[field] = sources[Math.floor(Math.random() * sources.length)];
                        break;
                    case 'Bank_Account':
                        data[field] = 'ACC' + Math.floor(Math.random() * **********).toString();
                        break;
                    case 'Risk_Rating':
                        const ratings = ['LOW', 'MEDIUM', 'HIGH'];
                        data[field] = ratings[Math.floor(Math.random() * ratings.length)];
                        break;
                    case 'PEP_Status':
                        data[field] = Math.random() > 0.95 ? 'YES' : 'NO';
                        break;
                    case 'Sanctions_Check':
                        data[field] = Math.random() > 0.98 ? 'FLAGGED' : 'CLEAR';
                        break;
                    default:
                        data[field] = 'Sample Data';
                }
            });
            
            return data;
        }

        async function generateCSV() {
            const rowCount = parseInt(document.getElementById('rowCount').value);
            const missingPercent = parseInt(document.getElementById('missingDataPercent').value);
            
            const generateBtn = document.getElementById('generateBtn');
            const progress = document.getElementById('progress');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            generateBtn.disabled = true;
            progress.style.display = 'block';
            
            try {
                // Create CSV header
                let csvContent = kycFields.join(',') + '\n';
                
                const chunkSize = 1000; // Process 1000 rows at a time
                const totalChunks = Math.ceil(rowCount / chunkSize);
                
                for (let chunk = 0; chunk < totalChunks; chunk++) {
                    const startRow = chunk * chunkSize;
                    const endRow = Math.min(startRow + chunkSize, rowCount);
                    
                    // Generate chunk data
                    for (let i = startRow; i < endRow; i++) {
                        const rowData = generateRandomData(missingPercent);
                        const csvRow = kycFields.map(field => {
                            const value = rowData[field] || '';
                            // Escape commas and quotes in CSV
                            return value.includes(',') || value.includes('"') ? `"${value.replace(/"/g, '""')}"` : value;
                        }).join(',');
                        csvContent += csvRow + '\n';
                    }
                    
                    // Update progress
                    const progress = ((chunk + 1) / totalChunks) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `Generated ${endRow.toLocaleString()} of ${rowCount.toLocaleString()} rows (${Math.round(progress)}%)`;
                    
                    // Allow UI to update
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
                
                // Create and download file
                progressText.textContent = 'Creating download file...';
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `large-test-kyc-${rowCount}-rows-${missingPercent}pct-missing.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                progressText.textContent = `✅ Successfully generated ${rowCount.toLocaleString()} rows!`;
                
            } catch (error) {
                console.error('Error generating CSV:', error);
                progressText.textContent = '❌ Error generating CSV: ' + error.message;
            } finally {
                generateBtn.disabled = false;
                setTimeout(() => {
                    progress.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 3000);
            }
        }
    </script>
</body>
</html>
