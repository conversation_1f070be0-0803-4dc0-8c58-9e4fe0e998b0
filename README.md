# KYC Data Analysis Tool

A comprehensive web-based application for analyzing blank/missing data in KYC (Know Your Customer) datasets from an AML (Anti-Money Laundering) compliance perspective.

## Features

### 📊 Data Analysis
- **File Upload**: Support for CSV and Excel (.xlsx, .xls) files
- **Drag & Drop**: Easy file upload with drag and drop functionality
- **Real-time Analysis**: Instant analysis of missing data patterns
- **Field-by-Field Analysis**: Detailed breakdown of blank data for each field

### 📈 Visualizations
- **Missing Data Chart**: Bar chart showing fields with highest missing data percentages
- **Completeness Distribution**: Doughnut chart showing profile completeness ranges
- **Interactive Charts**: Built with Chart.js for responsive visualizations

### 🚨 Risk Assessment
- **Risk Levels**: Automatic classification of fields as HIGH, MEDIUM, or LOW risk
- **Critical Fields**: Special attention to AML-critical fields
- **Compliance Scoring**: Overall completion rate and compliance assessment

### 📋 Reporting & Export
- **CSV Export**: Export analysis results to CSV format
- **PDF Reports**: Generate comprehensive PDF reports for compliance documentation
- **Incomplete Records**: Export only records that need attention
- **Print-Friendly**: Optimized layouts for printing

## Expected Data Fields

The tool is designed to analyze the following KYC fields:

**Critical AML Fields** (Higher risk weighting):
- CIF (Customer Identification File)
- FULL_NAME
- NATIONAL_IDENTIFIER
- DATE_OF_BIRTH
- NATIONALITY
- HOME_ADDRESS
- OCCUPATION

**Additional Fields**:
- CONTACT_NO
- NO_OF_PTO
- INDUSTRY_SECTOR
- CUSTOMER_SEGMENT
- CUSTOMER_SEGMENT_UDF
- INCOME_LEVEL
- MAILING_ADDRESS
- ADD01
- DEFAULT
- OFFICE_ADDRESS
- EMPLOYMENTSTATUS
- FATHERNAME
- NO_OF_SIGNATURE

## How to Use

1. **Open the Application**: Open `index.html` in your web browser
2. **Upload Data**: 
   - Click "Choose File" or drag and drop your CSV/Excel file
   - Supported formats: .csv, .xlsx, .xls
3. **Review Analysis**: 
   - View overall statistics in the dashboard
   - Examine field-by-field analysis in the detailed table
   - Check risk assessment for compliance insights
4. **Export Results**:
   - Export analysis to CSV for further processing
   - Generate PDF reports for compliance documentation
   - Export incomplete records for follow-up actions

## Risk Level Classification

### High Risk
- **Critical Fields**: >10% missing data
- **Other Fields**: >25% missing data

### Medium Risk
- **Critical Fields**: 5-10% missing data
- **Other Fields**: 15-25% missing data

### Low Risk
- **Critical Fields**: <5% missing data
- **Other Fields**: <15% missing data

## Sample Data

A sample CSV file (`sample-kyc-data.csv`) is included for testing purposes. This file contains 20 sample records with various missing data patterns to demonstrate the tool's capabilities.

## Technical Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- JavaScript enabled
- No server required - runs entirely in the browser

## Dependencies

- **Chart.js**: For data visualizations
- **SheetJS**: For Excel file processing
- **Modern CSS**: Responsive design with CSS Grid and Flexbox

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Security & Privacy

- All data processing happens locally in your browser
- No data is sent to external servers
- Files are processed in memory only
- No data persistence unless explicitly exported

## Compliance Features

- **AML Focus**: Special attention to anti-money laundering requirements
- **Risk-Based Approach**: Prioritizes critical fields for compliance
- **Audit Trail**: Comprehensive reporting for regulatory documentation
- **Data Quality Metrics**: Quantitative measures of data completeness

## Getting Started

1. Download all files to a local directory
2. Open `index.html` in your web browser
3. Upload your KYC dataset (CSV or Excel format)
4. Review the analysis and export results as needed

For testing, use the included `sample-kyc-data.csv` file to see how the tool works with sample data containing various missing data patterns.
