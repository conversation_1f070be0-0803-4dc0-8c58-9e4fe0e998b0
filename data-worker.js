// Web Worker for processing large KYC datasets
// This worker handles memory-intensive operations in the background

// Import XLSX library for Excel processing
try {
    importScripts('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js');
} catch (e) {
    console.warn('XLSX library not available in worker, Excel processing will be limited');
}

// Expected KYC fields
const kycFields = [
    'CIF', 'CONTACT_NO', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'NO_OF_PTO',
    'OCCUPATION', 'INDUSTRY_SECTOR', 'CUSTOMER_SEGMENT', 'CUSTOMER_SEGMENT_UDF',
    'INCOME_LEVEL', 'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'DEFAULT',
    'OFFICE_ADDRESS', 'EMPLOYMENTSTATUS', 'DATE_OF_BIRTH', 'NATIONALITY',
    'FATHERNAME', 'NO_OF_SIGNATURE'
];

// Critical fields for AML compliance
const criticalFields = [
    'CIF', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'DATE_OF_BIRTH',
    'NATIONALITY', 'HOME_ADDRESS', 'OCCUPATION'
];

// Fields with special validation rules
const specialValidationFields = ['NO_OF_PTO', 'NO_OF_SIGNATURE'];

/**
 * Check if a field value should be treated as blank based on special validation rules
 * @param {string} field - The field name
 * @param {*} value - The field value
 * @param {Object} row - The complete row data (required for DEFAULT field validation)
 * @param {Array} availableFields - Array of all available field names (required for DEFAULT field validation)
 * @returns {boolean} - True if the field should be treated as blank
 */
function isFieldValueBlank(field, value, row = null, availableFields = null) {
    // Special validation for DEFAULT field - reverse validation logic
    // DEFAULT field is only blank if ALL other fields in the record are blank
    if (field === 'DEFAULT' && row && availableFields) {
        const otherFields = availableFields.filter(f => f !== 'DEFAULT');
        const hasAnyOtherData = otherFields.some(otherField => {
            const otherValue = row[otherField];
            // Check if this other field has data using standard validation
            if (!otherValue ||
                otherValue.toString().trim() === '' ||
                otherValue.toString().toLowerCase() === 'null' ||
                otherValue.toString().toLowerCase() === 'undefined') {
                return false;
            }
            // Apply special validation for NO_OF_PTO and NO_OF_SIGNATURE
            if (specialValidationFields.includes(otherField) && otherValue.toString().trim() === '0') {
                return false;
            }
            return true;
        });

        // DEFAULT field is considered filled if ANY other field has data
        return !hasAnyOtherData;
    }

    // Standard blank checks for all other fields
    if (!value ||
        value.toString().trim() === '' ||
        value.toString().toLowerCase() === 'null' ||
        value.toString().toLowerCase() === 'undefined') {
        return true;
    }

    // Special validation for NO_OF_PTO and NO_OF_SIGNATURE fields
    // Value of "0" should be treated as BLANK/MISSING for compliance purposes
    if (specialValidationFields.includes(field) && value.toString().trim() === '0') {
        return true;
    }

    return false;
}

// Chunk size for processing large datasets
const CHUNK_SIZE = 1000;

self.onmessage = function(e) {
    const { type, data, fileData, fileName, isExcel } = e.data;

    switch (type) {
        case 'PROCESS_FILE':
            processFile(fileData, fileName, isExcel);
            break;
        case 'ANALYZE_DATA':
            analyzeData(data);
            break;
        case 'PROCESS_CHUNK':
            processChunk(data);
            break;
        default:
            console.error('Unknown message type:', type);
    }
};

function processFile(fileData, fileName, isExcel) {
    try {
        postMessage({
            type: 'PROGRESS_UPDATE',
            stage: 'upload',
            progress: 100,
            message: 'File uploaded successfully'
        });

        postMessage({
            type: 'PROGRESS_UPDATE',
            stage: 'parse',
            progress: 0,
            message: 'Starting file parsing...'
        });

        let data;

        if (isExcel) {
            data = parseExcel(fileData);
        } else {
            data = parseCSV(fileData);
        }

        postMessage({
            type: 'PROGRESS_UPDATE',
            stage: 'parse',
            progress: 100,
            message: `Parsed ${data.length.toLocaleString()} records`
        });

        // Start validation
        postMessage({
            type: 'PROGRESS_UPDATE',
            stage: 'validate',
            progress: 0,
            message: 'Validating data structure...'
        });

        const validationResult = validateData(data);
        
        postMessage({
            type: 'PROGRESS_UPDATE',
            stage: 'validate',
            progress: 100,
            message: 'Data validation complete'
        });

        // Start analysis
        postMessage({
            type: 'PROGRESS_UPDATE',
            stage: 'analyze',
            progress: 0,
            message: 'Starting data analysis...'
        });

        analyzeDataInChunks(data);

    } catch (error) {
        postMessage({
            type: 'ERROR',
            message: error.message
        });
    }
}

function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const headers = parseCSVLine(lines[0]);
    const data = [];
    
    const totalLines = lines.length - 1;
    
    for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
            const values = parseCSVLine(lines[i]);
            const row = {};
            headers.forEach((header, index) => {
                row[header.trim().replace(/"/g, '')] = values[index] || '';
            });
            data.push(row);
        }
        
        // Update progress every 1000 rows
        if (i % 1000 === 0) {
            const progress = Math.round((i / totalLines) * 100);
            postMessage({
                type: 'PROGRESS_UPDATE',
                stage: 'parse',
                progress: progress,
                message: `Parsing row ${i.toLocaleString()} of ${totalLines.toLocaleString()}`
            });
        }
    }
    
    return data;
}

function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim().replace(/^"|"$/g, ''));
            current = '';
        } else {
            current += char;
        }
    }
    result.push(current.trim().replace(/^"|"$/g, ''));
    return result;
}

function parseExcel(arrayBuffer) {
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // Get the range to estimate progress
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    const totalRows = range.e.r - range.s.r;
    
    postMessage({
        type: 'PROGRESS_UPDATE',
        stage: 'parse',
        progress: 50,
        message: `Processing ${totalRows.toLocaleString()} rows...`
    });
    
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    
    return jsonData;
}

function validateData(data) {
    if (!data || data.length === 0) {
        throw new Error('No data found in file');
    }
    
    const availableFields = Object.keys(data[0]);
    const missingCriticalFields = criticalFields.filter(field => !availableFields.includes(field));
    
    return {
        totalRecords: data.length,
        availableFields: availableFields,
        missingCriticalFields: missingCriticalFields,
        hasAllCriticalFields: missingCriticalFields.length === 0
    };
}

function analyzeDataInChunks(data) {
    const totalRecords = data.length;
    const availableFields = Object.keys(data[0]);
    
    // Initialize analysis results
    const fieldAnalysis = {};
    availableFields.forEach(field => {
        fieldAnalysis[field] = {
            totalRecords: totalRecords,
            blankCount: 0,
            blankPercentage: 0,
            riskLevel: 'LOW'
        };
    });
    
    const profileCompleteness = [];
    let processedRecords = 0;
    
    // Process data in chunks to avoid memory issues
    for (let i = 0; i < data.length; i += CHUNK_SIZE) {
        const chunk = data.slice(i, i + CHUNK_SIZE);
        
        chunk.forEach(row => {
            // Analyze each field for blank values with special validation
            availableFields.forEach(field => {
                if (isFieldValueBlank(field, row[field], row, availableFields)) {
                    fieldAnalysis[field].blankCount++;
                }
            });

            // Calculate profile completeness with special field validation
            const filledFields = availableFields.filter(field =>
                !isFieldValueBlank(field, row[field], row, availableFields)
            ).length;
            
            profileCompleteness.push((filledFields / availableFields.length) * 100);
            processedRecords++;
        });
        
        // Update progress
        const progress = Math.round((processedRecords / totalRecords) * 100);
        postMessage({
            type: 'PROGRESS_UPDATE',
            stage: 'analyze',
            progress: progress,
            message: `Analyzed ${processedRecords.toLocaleString()} of ${totalRecords.toLocaleString()} records`
        });
        
        // Allow other operations to run
        if (i % (CHUNK_SIZE * 10) === 0) {
            // Small delay to prevent blocking
            setTimeout(() => {}, 1);
        }
    }
    
    // Calculate final statistics
    availableFields.forEach(field => {
        const analysis = fieldAnalysis[field];
        analysis.blankPercentage = parseFloat(((analysis.blankCount / totalRecords) * 100).toFixed(2));
        analysis.riskLevel = getRiskLevel(field, analysis.blankPercentage);
    });
    
    const completeProfiles = profileCompleteness.filter(p => p === 100).length;
    const incompleteProfiles = totalRecords - completeProfiles;
    const completionRate = parseFloat(((completeProfiles / totalRecords) * 100).toFixed(1));
    
    // Send final results
    postMessage({
        type: 'ANALYSIS_COMPLETE',
        results: {
            fieldAnalysis,
            profileCompleteness,
            totalRecords,
            completeProfiles,
            incompleteProfiles,
            completionRate,
            availableFields
        }
    });
}

function getRiskLevel(field, blankPercentage) {
    const isCritical = criticalFields.includes(field);
    
    if (isCritical) {
        if (blankPercentage > 10) return 'HIGH';
        if (blankPercentage > 5) return 'MEDIUM';
        return 'LOW';
    } else {
        if (blankPercentage > 25) return 'HIGH';
        if (blankPercentage > 15) return 'MEDIUM';
        return 'LOW';
    }
}

// Memory cleanup function
function cleanup() {
    // Force garbage collection if available
    if (typeof gc === 'function') {
        gc();
    }
}
