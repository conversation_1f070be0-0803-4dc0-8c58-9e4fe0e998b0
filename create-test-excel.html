<!DOCTYPE html>
<html>
<head>
    <title>Create Test Excel File</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h2>Create Test Excel File</h2>
    <button onclick="createTestExcel()">Create Test Excel File</button>
    
    <script>
    function createTestExcel() {
        // Sample KYC data
        const data = [
            ["Customer ID", "Full Name", "Date of Birth", "Address", "Phone Number", "Email", "ID Type", "ID Number", "Occupation", "Income", "Source of Funds", "PEP Status", "Sanctions Check", "Risk Rating", "Account Type", "Branch", "Account Opening Date", "KYC Review Date", "Document Status", "Compliance Notes"],
            ["CUST001", "<PERSON>", "1985-03-15", "123 Main St New York NY 10001", "555-0123", "<EMAIL>", "Passport", "P123456789", "Engineer", "75000", "Salary", "No", "Clear", "Low", "Checking", "NYC Branch", "2023-01-15", "2023-01-15", "Complete", "All documents verified"],
            ["CUST002", "Jane Doe", "", "456 Oak Ave Los Angeles CA 90210", "555-0456", "<EMAIL>", "Driver License", "DL987654321", "Teacher", "55000", "Salary", "No", "Clear", "Low", "Savings", "LA Branch", "2023-02-20", "2023-02-20", "Incomplete", "Missing address proof"],
            ["CUST003", "Bob Johnson", "1978-11-22", "789 Pine St Chicago IL 60601", "", "<EMAIL>", "Passport", "P987654321", "Manager", "95000", "Salary", "No", "Clear", "Medium", "Business", "Chicago Branch", "2023-03-10", "2023-03-10", "Complete", ""],
            ["CUST004", "Alice Brown", "1990-07-08", "321 Elm St Miami FL 33101", "555-0789", "", "State ID", "ID456789123", "Consultant", "120000", "Business Income", "No", "Clear", "Medium", "Premium", "Miami Branch", "2023-04-05", "", "Pending", "Awaiting income verification"],
            ["CUST005", "Charlie Wilson", "1982-12-03", "654 Maple Dr Seattle WA 98101", "555-0321", "<EMAIL>", "", "CW789123456", "Developer", "85000", "Salary", "No", "Clear", "Low", "Checking", "Seattle Branch", "2023-05-12", "2023-05-12", "Complete", "All documents verified"]
        ];
        
        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(data);
        
        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, "KYC Data");
        
        // Save file
        XLSX.writeFile(wb, "test-kyc-data.xlsx");
        
        alert("Test Excel file 'test-kyc-data.xlsx' has been created and downloaded!");
    }
    </script>
</body>
</html>
