<!DOCTYPE html>
<html>
<head>
    <title>Test Excel Export</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h2>Test Excel Export Functionality</h2>
    <button onclick="testExcelExport()">Test Excel Export</button>
    <div id="status"></div>

    <script>
        function testExcelExport() {
            const status = document.getElementById('status');
            status.innerHTML = 'Testing Excel export...';
            
            try {
                // Create test data
                const testData = [
                    { CIF: '12345', FULL_NAME: '<PERSON>', ADD01: '123 Main St', NO_OF_PTO: '2' },
                    { CIF: '12346', FULL_NAME: '<PERSON>', ADD01: '', NO_OF_PTO: '0' },
                    { CIF: '12347', FULL_NAME: '', ADD01: '456 Oak Ave', NO_OF_PTO: '1' }
                ];
                
                const fields = ['CIF', 'FULL_NAME', 'ADD01', 'NO_OF_PTO'];
                
                // Create workbook
                const workbook = XLSX.utils.book_new();
                
                // Create summary sheet
                const summaryData = [
                    ['Test KYC Export'],
                    ['Generated:', new Date().toLocaleString()],
                    ['Records:', testData.length],
                    ['Fields:', fields.length]
                ];
                
                const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
                XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
                
                // Create field sheets
                fields.forEach(fieldName => {
                    const sheetData = [
                        [fieldName + ' - Field Analysis'],
                        ['Record #', fieldName, 'Status']
                    ];
                    
                    testData.forEach((row, index) => {
                        const value = row[fieldName] || '';
                        const status = value === '' || value === '0' ? 'BLANK' : 'FILLED';
                        sheetData.push([index + 1, value, status]);
                    });
                    
                    const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
                    worksheet['!cols'] = [
                        { width: 10 },
                        { width: 30 },
                        { width: 10 }
                    ];
                    
                    XLSX.utils.book_append_sheet(workbook, worksheet, fieldName);
                });
                
                // Save file
                XLSX.writeFile(workbook, 'test-kyc-export.xlsx');
                
                status.innerHTML = '<span style="color: green;">✅ Excel export test successful! File saved as test-kyc-export.xlsx</span>';
                
            } catch (error) {
                status.innerHTML = '<span style="color: red;">❌ Excel export test failed: ' + error.message + '</span>';
                console.error('Test error:', error);
            }
        }
    </script>
</body>
</html>
