// Global variables
let currentData = [];
let analysisResults = {};
let missingData<PERSON><PERSON> = null;
let completenessChart = null;
let dataWorker = null;
let currentPage = 1;
let itemsPerPage = 20;
let filteredData = [];
let sortColumn = null;
let sortDirection = 'asc';
let processingTimeout = null;
let progressHeartbeat = null;
let lastProgressTime = Date.now();
let debugConsoleVisible = false;
let processingStartTime = null;
let processingPaused = false;

// Expected KYC fields
const kycFields = [
    'CIF', 'CONTACT_NO', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'NO_OF_PTO',
    'OCCUPATION', 'INDUSTRY_SECTOR', 'CUSTOMER_SEGMENT', 'CUSTOMER_SEGMENT_UDF',
    'INCOME_LEVEL', 'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'DEFAULT',
    'OFFICE_ADDRESS', '<PERSON>MPLOYMENTSTATUS', 'DATE_OF_BIRTH', 'NATIONALITY',
    'FATHERNAME', 'NO_OF_SIGNATURE'
];

// Critical fields for AML compliance
const criticalFields = [
    'CIF', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'DATE_OF_BIRTH',
    'NATIONALITY', 'ADDRESS_GROUP', 'OCCUPATION'
];

// Fields with special validation rules
const specialValidationFields = ['NO_OF_PTO', 'NO_OF_SIGNATURE'];

/**
 * Check if a field value should be treated as blank based on special validation rules
 * @param {string} field - The field name
 * @param {*} value - The field value
 * @param {Object} row - The complete row data (required for DEFAULT field validation)
 * @param {Array} availableFields - Array of all available field names (required for DEFAULT field validation)
 * @returns {boolean} - True if the field should be treated as blank
 */
function isFieldValueBlank(field, value, row = null, availableFields = null) {
    // Special validation for DEFAULT field - reverse validation logic
    // DEFAULT field is only blank if ALL other fields in the record are blank
    if (field === 'DEFAULT' && row && availableFields) {
        const otherFields = availableFields.filter(f => f !== 'DEFAULT');
        const hasAnyOtherData = otherFields.some(otherField => {
            const otherValue = row[otherField];
            // Check if this other field has data using standard validation
            if (!otherValue ||
                otherValue.toString().trim() === '' ||
                otherValue.toString().toLowerCase() === 'null' ||
                otherValue.toString().toLowerCase() === 'undefined') {
                return false;
            }
            // Apply special validation for NO_OF_PTO and NO_OF_SIGNATURE
            if (specialValidationFields.includes(otherField) && otherValue.toString().trim() === '0') {
                return false;
            }
            return true;
        });

        // DEFAULT field is considered filled if ANY other field has data
        return !hasAnyOtherData;
    }

    // Standard blank checks for all other fields
    if (!value ||
        value.toString().trim() === '' ||
        value.toString().toLowerCase() === 'null' ||
        value.toString().toLowerCase() === 'undefined') {
        return true;
    }

    // Special validation for NO_OF_PTO and NO_OF_SIGNATURE fields
    // Value of "0" should be treated as BLANK/MISSING for compliance purposes
    if (specialValidationFields.includes(field) && value.toString().trim() === '0') {
        return true;
    }

    return false;
}

/**
 * Test function to verify special field validation logic
 * This function can be called from the browser console for testing
 */
function testSpecialValidation() {
    console.log('Testing special field validation logic...');

    // Test cases for NO_OF_PTO field
    console.log('NO_OF_PTO tests:');
    console.log('  Value "0":', isFieldValueBlank('NO_OF_PTO', '0')); // Should be true
    console.log('  Value "1":', isFieldValueBlank('NO_OF_PTO', '1')); // Should be false
    console.log('  Value "2":', isFieldValueBlank('NO_OF_PTO', '2')); // Should be false
    console.log('  Value "":', isFieldValueBlank('NO_OF_PTO', '')); // Should be true
    console.log('  Value null:', isFieldValueBlank('NO_OF_PTO', null)); // Should be true

    // Test cases for NO_OF_SIGNATURE field
    console.log('NO_OF_SIGNATURE tests:');
    console.log('  Value "0":', isFieldValueBlank('NO_OF_SIGNATURE', '0')); // Should be true
    console.log('  Value "1":', isFieldValueBlank('NO_OF_SIGNATURE', '1')); // Should be false
    console.log('  Value "3":', isFieldValueBlank('NO_OF_SIGNATURE', '3')); // Should be false
    console.log('  Value "":', isFieldValueBlank('NO_OF_SIGNATURE', '')); // Should be true

    // Test cases for regular fields (should not be affected)
    console.log('Regular field tests:');
    console.log('  FULL_NAME "0":', isFieldValueBlank('FULL_NAME', '0')); // Should be false
    console.log('  CONTACT_NO "0":', isFieldValueBlank('CONTACT_NO', '0')); // Should be false
    console.log('  FULL_NAME "":', isFieldValueBlank('FULL_NAME', '')); // Should be true

    // Test cases for DEFAULT field (reverse validation)
    console.log('DEFAULT field tests:');
    const testFields = ['CIF', 'FULL_NAME', 'CONTACT_NO', 'DEFAULT'];

    // Test case 1: Record with other data - DEFAULT should be considered filled
    const recordWithData = { CIF: '12345', FULL_NAME: 'John Doe', CONTACT_NO: '', DEFAULT: '' };
    console.log('  DEFAULT with other data present:', isFieldValueBlank('DEFAULT', '', recordWithData, testFields)); // Should be false

    // Test case 2: Record with no other data - DEFAULT should be considered blank
    const recordWithoutData = { CIF: '', FULL_NAME: '', CONTACT_NO: '', DEFAULT: 'some value' };
    console.log('  DEFAULT with no other data:', isFieldValueBlank('DEFAULT', 'some value', recordWithoutData, testFields)); // Should be true

    // Test case 3: Record with only special field "0" values - DEFAULT should be blank
    const recordWithZeros = { CIF: '', NO_OF_PTO: '0', NO_OF_SIGNATURE: '0', DEFAULT: 'value' };
    const testFieldsWithSpecial = ['CIF', 'NO_OF_PTO', 'NO_OF_SIGNATURE', 'DEFAULT'];
    console.log('  DEFAULT with only zero values:', isFieldValueBlank('DEFAULT', 'value', recordWithZeros, testFieldsWithSpecial)); // Should be true

    // Test case 4: Record with mixed data including valid special fields - DEFAULT should be filled
    const recordWithValidSpecial = { CIF: '', NO_OF_PTO: '2', NO_OF_SIGNATURE: '0', DEFAULT: 'value' };
    console.log('  DEFAULT with valid special field data:', isFieldValueBlank('DEFAULT', 'value', recordWithValidSpecial, testFieldsWithSpecial)); // Should be false

    // Test case 5: Record with null/undefined values - DEFAULT should be blank
    const recordWithNulls = { CIF: null, FULL_NAME: undefined, CONTACT_NO: 'null', DEFAULT: 'value' };
    console.log('  DEFAULT with null/undefined values:', isFieldValueBlank('DEFAULT', 'value', recordWithNulls, testFields)); // Should be true

    console.log('Special validation testing complete!');
}

// Address fields that form a group requirement
const addressFields = [
    'ADD01',           // Primary Address
    'OFFICE_ADDRESS',  // Business Address
    'MAILING_ADDRESS', // Correspondence Address
    'HOME_ADDRESS'     // Residential Address
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupFileUpload();
    setupTableControls();
    initializeWorker();

    // Check if recalculation is needed for existing data
    setTimeout(() => {
        checkAndPromptRecalculation();
    }, 1000);
});

function initializeWorker() {
    // Disable Web Workers for file:// protocol compatibility
    // All processing will happen in main thread with progress updates
    dataWorker = null;
    console.log('✅ Application initialized for file:// protocol - Main thread processing enabled');
}

// Web Worker handlers removed - using main thread processing only

function updateProgressIndicator(stage, progress, message) {
    // Update heartbeat timer
    lastProgressTime = Date.now();

    // Update overall progress
    const stages = ['upload', 'parse', 'validate', 'analyze'];
    const stageIndex = stages.indexOf(stage);
    const overallProgress = (stageIndex * 25) + (progress * 0.25);

    document.getElementById('overallProgress').style.width = overallProgress + '%';
    document.getElementById('overallProgressText').textContent = message;

    // Update stage-specific progress
    const stageElement = document.getElementById(stage + 'Stage');
    const progressElement = document.getElementById(stage + 'Progress');
    const textElement = document.getElementById(stage + 'Text');

    if (stageElement && progressElement && textElement) {
        progressElement.style.width = progress + '%';
        textElement.textContent = message;

        // Update stage status
        stageElement.classList.remove('active', 'completed');
        if (progress === 100) {
            stageElement.classList.add('completed');
        } else if (progress > 0) {
            stageElement.classList.add('active');
        }
    }

    // Update estimated time
    if (overallProgress > 0 && overallProgress < 100) {
        const estimatedTime = calculateEstimatedTime(overallProgress);
        document.getElementById('estimatedTime').textContent = `Est. ${estimatedTime}`;
    }

    // Update debug stats if console is visible
    if (debugConsoleVisible) {
        updateDebugStats();
    }
}

function calculateEstimatedTime(progress) {
    // Simple estimation based on current progress
    const elapsed = Date.now() - (window.processingStartTime || Date.now());
    const remaining = (elapsed / progress) * (100 - progress);

    if (remaining < 60000) {
        return Math.round(remaining / 1000) + 's remaining';
    } else {
        return Math.round(remaining / 60000) + 'm remaining';
    }
}

function showProgressIndicators() {
    document.getElementById('progressSection').style.display = 'block';
    // Remove modal overlay to prevent blocking
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
    window.processingStartTime = Date.now();
    processingStartTime = Date.now();

    // Show debug controls
    const pauseBtn = document.getElementById('pauseProcessing');
    if (pauseBtn) {
        pauseBtn.style.display = 'inline-flex';
    }

    logDebug('info', 'Processing started - Progress indicators shown');
}

function hideProgressIndicators() {
    document.getElementById('progressSection').style.display = 'none';
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }

    // Hide debug controls
    const pauseBtn = document.getElementById('pauseProcessing');
    if (pauseBtn) {
        pauseBtn.style.display = 'none';
    }

    logDebug('success', 'Processing completed - Progress indicators hidden');
}

function setupFileUpload() {
    const fileInput = document.getElementById('fileInput');
    const fileUploadArea = document.getElementById('fileUploadArea');

    // Handle file selection
    fileInput.addEventListener('change', handleFileSelect);

    // Handle drag and drop
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#764ba2';
        fileUploadArea.style.backgroundColor = '#f8f9ff';
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#667eea';
        fileUploadArea.style.backgroundColor = '';
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#667eea';
        fileUploadArea.style.backgroundColor = '';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    if (!file) return;

    logDebug('info', 'File upload started', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        fileSizeMB: (file.size / (1024 * 1024)).toFixed(2)
    });

    // Validate file size (300MB limit)
    const maxSize = 300 * 1024 * 1024; // 300MB
    const fileSizeMB = file.size / (1024 * 1024);

    if (file.size > maxSize) {
        const errorMsg = `File size (${fileSizeMB.toFixed(1)}MB) exceeds the maximum limit of 300MB. Please use a smaller file.`;
        showErrorMessageWithDebug(errorMsg);
        return;
    }

    // Show warning for large Excel files
    if (file.name.match(/\.(xlsx|xls)$/i) && fileSizeMB > 50) {
        logDebug('warn', `Large Excel file detected: ${fileSizeMB.toFixed(1)}MB`);
        showLargeFileWarning(fileSizeMB);
    }

    // Validate file type
    const validExtensions = ['.csv', '.xlsx', '.xls'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (!validExtensions.includes(fileExtension)) {
        showErrorMessageWithDebug('Please upload a CSV or Excel file (.csv, .xlsx, .xls)');
        return;
    }

    // Display file info
    displayFileInfo(file);

    // Show progress indicators
    showProgressIndicators();

    // Start processing timeout
    startProcessingTimeout();

    // Hide analysis section
    document.getElementById('analysisSection').style.display = 'none';

    // Processing in main thread with chunked processing to maintain UI responsiveness
    logDebug('info', 'Starting file processing in main thread with optimized chunking');

    // Process file based on type
    if (file.name.endsWith('.csv')) {
        logDebug('info', 'Processing CSV file');
        processLargeCSV(file);
    } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        logDebug('info', 'Processing Excel file');
        processLargeExcel(file);
    } else {
        showErrorMessageWithDebug('Please upload a CSV or Excel file.');
        hideProgressIndicators();
    }
}

function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fef2f2;
        border: 1px solid #fca5a5;
        color: #dc2626;
        padding: 20px;
        border-radius: 8px;
        z-index: 1001;
        max-width: 500px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        white-space: pre-line;
    `;
    errorDiv.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #dc2626;"></i>
        </div>
        <div style="margin-bottom: 15px; font-weight: 500;">${message}</div>
        <button onclick="this.parentElement.remove()" style="background: #dc2626; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">OK</button>
    `;
    document.body.appendChild(errorDiv);

    // Auto-hide after 8 seconds for longer messages
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 8000);
}

function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #f0f9ff;
        border: 1px solid #7dd3fc;
        color: #0369a1;
        padding: 20px;
        border-radius: 8px;
        z-index: 1001;
        max-width: 500px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        white-space: pre-line;
    `;
    successDiv.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-check-circle" style="font-size: 24px; color: #059669;"></i>
        </div>
        <div style="margin-bottom: 15px; font-weight: 500;">${message}</div>
        <button onclick="this.parentElement.remove()" style="background: #059669; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">OK</button>
    `;
    document.body.appendChild(successDiv);

    // Auto-hide after 6 seconds
    setTimeout(() => {
        if (successDiv.parentElement) {
            successDiv.remove();
        }
    }, 6000);
}

function showLargeFileWarning(fileSizeMB) {
    const warningDiv = document.createElement('div');
    warningDiv.style.cssText = `
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fef3c7;
        border: 1px solid #f59e0b;
        color: #92400e;
        padding: 20px;
        border-radius: 8px;
        z-index: 1000;
        max-width: 500px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    `;
    warningDiv.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #f59e0b;"></i>
        </div>
        <div style="margin-bottom: 15px; font-weight: 500;">
            <strong>Large Excel File Warning</strong><br>
            File size: ${fileSizeMB.toFixed(1)}MB<br><br>
            Large Excel files may cause memory issues. For better performance, consider converting to CSV format.
        </div>
        <div style="display: flex; gap: 10px; justify-content: center;">
            <button onclick="this.parentElement.remove()" style="background: #f59e0b; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Continue Anyway</button>
            <button onclick="this.parentElement.remove()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Cancel</button>
        </div>
    `;
    document.body.appendChild(warningDiv);

    // Auto-hide after 10 seconds
    setTimeout(() => {
        if (warningDiv.parentElement) {
            warningDiv.remove();
        }
    }, 10000);
}

// Removed duplicate function - using the more comprehensive streaming version below

function processLargeExcel(file) {
    logDebug('info', 'Starting Excel file processing', {
        fileName: file.name,
        fileSize: file.size,
        fileSizeMB: (file.size / (1024 * 1024)).toFixed(2)
    });

    const fileSizeMB = file.size / (1024 * 1024);

    if (fileSizeMB > 300) {
        const errorMsg = `Excel file is ${fileSizeMB.toFixed(1)}MB. Maximum supported file size is 300MB.`;
        showErrorMessageWithDebug(errorMsg);
        return;
    }

    // For large Excel files (>50MB), use streaming approach to prevent memory crashes
    // For very large files (>150MB), use enhanced streaming with smaller chunks
    if (fileSizeMB > 50) {
        logDebug('info', `Large Excel file detected: ${fileSizeMB.toFixed(1)}MB - Using streaming processing to prevent memory issues`);
        processExcelStreaming(file);
        return;
    }

    // For smaller files, use the standard approach
    logDebug('info', 'Using standard Excel processing for smaller file');

    const reader = new FileReader();

    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const progress = Math.round((e.loaded / e.total) * 100);
            updateProgressIndicator('upload', progress, `Uploading... ${progress}%`);
            logDebug('info', `Excel file upload progress: ${progress}%`);
        }
    };

    reader.onload = function(e) {
        updateProgressIndicator('upload', 100, 'Upload complete');
        logDebug('info', 'Excel file upload complete, starting processing');

        // Check if the array buffer is valid
        if (!e.target.result || e.target.result.byteLength === 0) {
            hideProgressIndicators();
            showErrorMessageWithDebug('File appears to be empty or corrupted. Please try a different file.');
            return;
        }

        // Process Excel data directly in main thread
        setTimeout(() => {
            processExcelFallback(e.target.result, file.name);
        }, 100); // Small delay to allow UI update
    };

    reader.onerror = function(error) {
        logDebug('error', 'FileReader error during Excel processing', {
            error: error,
            fileName: file.name,
            fileSize: file.size
        });
        hideProgressIndicators();
        showErrorMessageWithDebug('Error reading file. The file may be corrupted or too large. Please try again with a different file.');
    };

    // Add timeout for very large files (10 minutes to match overall processing timeout)
    setTimeout(() => {
        if (reader.readyState === FileReader.LOADING) {
            reader.abort();
            hideProgressIndicators();
            logDebug('error', 'Excel file reading timeout after 10 minutes', {
                fileName: file.name,
                fileSize: file.size,
                fileSizeMB: (file.size / (1024 * 1024)).toFixed(2)
            });
            showErrorMessageWithDebug('Excel file reading timeout after 10 minutes. The file may be extremely large or there may be a processing issue.');
        }
    }, 600000); // 10 minute timeout (same as overall processing timeout)

    reader.readAsArrayBuffer(file);
}

// Streaming Excel processor for large files to prevent memory crashes
function processExcelStreaming(file) {
    logDebug('info', 'Starting streaming Excel processing', {
        fileName: file.name,
        fileSize: file.size,
        fileSizeMB: (file.size / (1024 * 1024)).toFixed(2)
    });

    showProgressIndicators();
    updateProgressIndicator('upload', 0, 'Preparing streaming Excel processing...');

    // Read file in chunks to prevent memory overload
    // Use smaller chunks for very large files to prevent memory issues
    const fileSizeMB = file.size / (1024 * 1024);
    const chunkSize = fileSizeMB > 150 ? 1 * 1024 * 1024 : 2 * 1024 * 1024; // 1MB for >150MB files, 2MB for smaller
    let offset = 0;
    let chunks = [];
    let totalChunks = Math.ceil(file.size / chunkSize);

    logDebug('info', 'Optimized chunk size for file size', {
        fileSizeMB: fileSizeMB.toFixed(1),
        chunkSizeMB: (chunkSize / (1024 * 1024)).toFixed(1),
        totalChunks: totalChunks
    });

    function readNextChunk() {
        if (offset >= file.size) {
            // All chunks read, now process
            processExcelChunks(chunks, file.name);
            return;
        }

        const chunk = file.slice(offset, offset + chunkSize);
        const reader = new FileReader();
        const chunkIndex = Math.floor(offset / chunkSize);

        reader.onload = function(e) {
            chunks.push(new Uint8Array(e.target.result));
            offset += chunkSize;

            const progress = Math.min(100, (chunkIndex / totalChunks) * 100);
            updateProgressIndicator('upload', progress, `Reading chunk ${chunkIndex + 1} of ${totalChunks}...`);

            logDebug('info', `Chunk ${chunkIndex + 1}/${totalChunks} read`, {
                chunkSize: e.target.result.byteLength,
                totalProgress: progress.toFixed(1) + '%'
            });

            // Use setTimeout to prevent UI blocking
            setTimeout(readNextChunk, 10);
        };

        reader.onerror = function() {
            logDebug('error', 'Error reading Excel chunk', {
                chunkIndex: chunkIndex,
                offset: offset
            });
            hideProgressIndicators();
            showErrorMessageWithDebug('Error reading Excel file chunk. The file may be corrupted.');
        };

        reader.readAsArrayBuffer(chunk);
    }

    // Start reading chunks
    readNextChunk();
}

function processExcelChunks(chunks, fileName) {
    try {
        updateProgressIndicator('parse', 0, 'Assembling Excel file from chunks...');

        // Calculate total size
        const totalSize = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        logDebug('info', 'Assembling Excel chunks', {
            chunkCount: chunks.length,
            totalSize: totalSize,
            totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2)
        });

        // Combine chunks into single array buffer efficiently
        const combinedArray = new Uint8Array(totalSize);
        let offset = 0;

        for (let i = 0; i < chunks.length; i++) {
            combinedArray.set(chunks[i], offset);
            offset += chunks[i].length;

            // Update progress
            const progress = Math.min(50, (i / chunks.length) * 50);
            updateProgressIndicator('parse', progress, `Assembling chunk ${i + 1} of ${chunks.length}...`);

            // Clear chunk from memory immediately
            chunks[i] = null;
        }

        // Clear chunks array
        chunks.length = 0;
        chunks = null;

        updateProgressIndicator('parse', 50, 'Excel file assembled, starting parsing...');

        // Force garbage collection hint
        if (window.gc) {
            window.gc();
        }

        // Process the combined array with streaming approach
        setTimeout(() => {
            processExcelStreamingParse(combinedArray.buffer, fileName);
        }, 100);

    } catch (error) {
        logDebug('error', 'Error assembling Excel chunks', {
            error: error.message,
            stack: error.stack
        });
        hideProgressIndicators();
        showErrorMessageWithDebug('Error assembling Excel file: ' + error.message);
    }
}

function processExcelStreamingParse(arrayBuffer, fileName) {
    try {
        const fileSizeMB = arrayBuffer.byteLength / (1024 * 1024);
        logDebug('info', 'Starting streaming Excel parsing', {
            fileName: fileName,
            fileSizeMB: fileSizeMB.toFixed(2)
        });

        updateProgressIndicator('parse', 60, 'Parsing Excel structure with minimal memory usage...');

        // Use absolute minimal options to reduce memory usage
        const streamingOptions = {
            type: 'array',
            cellDates: false,
            cellNF: false,
            cellStyles: false,
            sheetStubs: false,
            bookDeps: false,
            bookFiles: false,
            bookProps: false,
            bookSheets: false,
            bookVBA: false,
            dense: true,
            raw: true
        };

        setTimeout(() => {
            try {
                logDebug('info', 'Reading Excel workbook with streaming options');
                const workbook = XLSX.read(arrayBuffer, streamingOptions);

                // Clear array buffer immediately after reading
                arrayBuffer = null;

                // Force garbage collection
                if (window.gc) {
                    window.gc();
                }

                updateProgressIndicator('parse', 80, 'Excel workbook loaded, extracting data...');

                if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                    throw new Error('No worksheets found in Excel file');
                }

                // Enhanced worksheet detection - try multiple methods
                let worksheet = null;
                let sheetName = null;

                logDebug('info', 'Analyzing worksheets for data', {
                    availableSheets: workbook.SheetNames,
                    totalSheets: workbook.SheetNames.length
                });

                // Method 1: Check for !ref property (standard approach)
                for (const name of workbook.SheetNames) {
                    const sheet = workbook.Sheets[name];
                    if (sheet && sheet['!ref']) {
                        worksheet = sheet;
                        sheetName = name;
                        logDebug('info', `Found worksheet with !ref: "${name}"`, {
                            range: sheet['!ref']
                        });
                        break;
                    }
                }

                // Method 2: If no !ref found, check for any cell data
                if (!worksheet) {
                    logDebug('info', 'No !ref found, checking for cell data directly');
                    for (const name of workbook.SheetNames) {
                        const sheet = workbook.Sheets[name];
                        if (sheet) {
                            const cellKeys = Object.keys(sheet).filter(key => !key.startsWith('!'));
                            if (cellKeys.length > 0) {
                                worksheet = sheet;
                                sheetName = name;
                                logDebug('info', `Found worksheet with cell data: "${name}"`, {
                                    cellCount: cellKeys.length,
                                    sampleCells: cellKeys.slice(0, 5)
                                });
                                break;
                            }
                        }
                    }
                }

                // Method 3: If still no data, try to convert each sheet and check results
                if (!worksheet) {
                    logDebug('info', 'No cell data found, trying JSON conversion test');
                    for (const name of workbook.SheetNames) {
                        const sheet = workbook.Sheets[name];
                        if (sheet) {
                            try {
                                const testData = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '', blankrows: false });
                                if (testData && testData.length > 0) {
                                    worksheet = sheet;
                                    sheetName = name;
                                    logDebug('info', `Found worksheet with convertible data: "${name}"`, {
                                        rowCount: testData.length,
                                        firstRowSample: testData[0]
                                    });
                                    break;
                                }
                            } catch (testError) {
                                logDebug('warn', `Sheet "${name}" conversion test failed: ${testError.message}`);
                            }
                        }
                    }
                }

                if (!worksheet) {
                    // Provide detailed information about what was found
                    const sheetInfo = workbook.SheetNames.map(name => {
                        const sheet = workbook.Sheets[name];
                        return {
                            name: name,
                            hasRef: !!(sheet && sheet['!ref']),
                            refValue: sheet ? sheet['!ref'] : null,
                            cellCount: sheet ? Object.keys(sheet).filter(key => !key.startsWith('!')).length : 0,
                            properties: sheet ? Object.keys(sheet).filter(key => key.startsWith('!')) : []
                        };
                    });

                    logDebug('error', 'No worksheets contain data - detailed analysis', {
                        sheetAnalysis: sheetInfo,
                        workbookProperties: Object.keys(workbook)
                    });

                    throw new Error(`No worksheets contain data. Found ${workbook.SheetNames.length} sheets: ${workbook.SheetNames.join(', ')}. This may indicate an empty file, password protection, or unsupported Excel format.`);
                }

                logDebug('info', `Processing worksheet: "${sheetName}"`);
                updateProgressIndicator('parse', 90, `Processing data from sheet: ${sheetName}`);

                // Stream the JSON conversion
                setTimeout(() => {
                    processExcelStreamingConvert(worksheet, fileName);
                }, 50);

            } catch (parseError) {
                logDebug('error', 'Excel streaming parse error - trying fallback options', {
                    error: parseError.message,
                    stack: parseError.stack
                });

                // Try fallback parsing with different options
                setTimeout(() => {
                    tryExcelFallbackParsing(arrayBuffer, fileName, parseError);
                }, 100);
            }
        }, 100);

    } catch (error) {
        logDebug('error', 'Excel streaming processing error', {
            error: error.message,
            stack: error.stack
        });
        hideProgressIndicators();
        showErrorMessageWithDebug('Error in streaming Excel processing: ' + error.message);
    }
}

function processExcelStreamingConvert(worksheet, fileName) {
    try {
        updateProgressIndicator('parse', 95, 'Converting Excel data with memory optimization...');

        // Use streaming JSON conversion with minimal memory footprint
        const jsonOptions = {
            header: 1,
            defval: '',
            blankrows: false,
            raw: true
        };

        logDebug('info', 'Starting streaming JSON conversion');

        setTimeout(() => {
            try {
                // Convert to JSON array format
                const rawData = XLSX.utils.sheet_to_json(worksheet, jsonOptions);

                // Clear worksheet from memory immediately
                worksheet = null;

                if (!rawData || rawData.length < 2) {
                    throw new Error('Excel file must contain at least a header row and one data row');
                }

                logDebug('info', 'JSON conversion complete, starting streaming data processing', {
                    totalRows: rawData.length,
                    dataRows: rawData.length - 1
                });

                updateProgressIndicator('parse', 100, 'Excel parsing complete');

                // Start streaming data conversion
                setTimeout(() => {
                    processExcelDataStreaming(rawData, fileName);
                }, 50);

            } catch (conversionError) {
                logDebug('error', 'Excel streaming conversion error', {
                    error: conversionError.message,
                    stack: conversionError.stack
                });
                hideProgressIndicators();
                showErrorMessageWithDebug('Error converting Excel data: ' + conversionError.message);
            }
        }, 50);

    } catch (error) {
        logDebug('error', 'Excel streaming convert error', {
            error: error.message,
            stack: error.stack
        });
        hideProgressIndicators();
        showErrorMessageWithDebug('Error in Excel streaming conversion: ' + error.message);
    }
}

function processExcelDataStreaming(rawData, fileName) {
    try {
        updateProgressIndicator('validate', 0, 'Starting streaming data conversion...');

        const headers = rawData[0];
        const totalRows = rawData.length - 1;
        const data = [];

        logDebug('info', 'Starting streaming data conversion', {
            headers: headers.length,
            totalRows: totalRows,
            estimatedMemoryMB: ((totalRows * headers.length * 50) / (1024 * 1024)).toFixed(2)
        });

        // Use very small chunks for memory efficiency - smaller chunks for larger files
        const fileSizeMB = (totalRows * headers.length * 50) / (1024 * 1024); // Estimated size
        const baseChunkSize = fileSizeMB > 150 ? 500 : 1000; // Smaller chunks for very large files
        const chunkSize = Math.min(baseChunkSize, Math.max(100, Math.floor(10000 / headers.length)));
        let processedRows = 0;

        logDebug('info', 'Optimized processing chunk size', {
            estimatedSizeMB: fileSizeMB.toFixed(1),
            chunkSize: chunkSize,
            totalRows: totalRows,
            headers: headers.length
        });

        function processDataChunk() {
            try {
                const startIdx = processedRows + 1; // +1 to skip header
                const endIdx = Math.min(startIdx + chunkSize, rawData.length);
                const chunkData = [];

                // Process chunk
                for (let i = startIdx; i < endIdx; i++) {
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = rawData[i][index] || '';
                    });
                    chunkData.push(row);
                    processedRows++;
                }

                // Add chunk to main data
                data.push(...chunkData);

                const progress = Math.min(99, (processedRows / totalRows) * 100);
                updateProgressIndicator('validate', progress, `Processed ${processedRows.toLocaleString()} of ${totalRows.toLocaleString()} rows...`);

                logDebug('info', `Processed chunk: ${processedRows}/${totalRows} rows (${progress.toFixed(1)}%)`);

                if (processedRows < totalRows) {
                    // Continue with next chunk after short delay
                    setTimeout(processDataChunk, 5);
                } else {
                    // Processing complete
                    updateProgressIndicator('validate', 100, 'Data conversion complete');

                    // Clear raw data from memory
                    rawData.length = 0;

                    logDebug('success', `Streaming Excel processing complete: ${data.length.toLocaleString()} rows processed`, {
                        totalRows: data.length,
                        processingTime: Date.now() - processingStartTime,
                        memoryUsage: performance.memory ? {
                            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
                        } : 'unavailable'
                    });

                    // Store data and start analysis
                    currentData = data;
                    const recordCountEl = document.getElementById('recordCount');
                    if (recordCountEl) recordCountEl.textContent = data.length.toLocaleString();

                    // Force garbage collection before analysis
                    if (window.gc) {
                        window.gc();
                    }

                    // Start progressive analysis
                    setTimeout(() => {
                        analyzeDataProgressive();
                    }, 100);
                }

            } catch (chunkError) {
                logDebug('error', 'Error processing data chunk', {
                    error: chunkError.message,
                    chunkStart: startIdx,
                    chunkEnd: endIdx,
                    processedRows: processedRows
                });
                hideProgressIndicators();
                showErrorMessageWithDebug('Error processing Excel data chunk: ' + chunkError.message);
            }
        }

        // Start streaming data processing
        processDataChunk();

    } catch (error) {
        logDebug('error', 'Excel data streaming error', {
            error: error.message,
            stack: error.stack
        });
        hideProgressIndicators();
        showErrorMessageWithDebug('Error in Excel data streaming: ' + error.message);
    }
}

function tryExcelFallbackParsing(arrayBuffer, fileName, originalError) {
    try {
        logDebug('info', 'Attempting Excel fallback parsing with alternative options');
        updateProgressIndicator('parse', 70, 'Trying alternative Excel parsing methods...');

        // Try different parsing options in sequence
        const fallbackOptions = [
            // Option 1: Minimal options without dense/raw
            {
                type: 'array',
                cellDates: false,
                cellStyles: false,
                raw: false,
                dense: false
            },
            // Option 2: Only basic options
            {
                type: 'array',
                cellDates: false
            },
            // Option 3: Default options
            {
                type: 'array'
            },
            // Option 4: Try as binary
            {
                type: 'binary'
            }
        ];

        let optionIndex = 0;

        function tryNextOption() {
            if (optionIndex >= fallbackOptions.length) {
                // All options failed
                logDebug('error', 'All Excel parsing options failed', {
                    originalError: originalError.message,
                    optionsTried: fallbackOptions.length
                });
                hideProgressIndicators();
                showErrorMessageWithDebug(`Unable to parse Excel file. Original error: ${originalError.message}. This may indicate:\n• Password-protected file\n• Corrupted Excel file\n• Unsupported Excel format\n• File is not actually an Excel file\n\nTry:\n• Converting to CSV format\n• Saving as .xlsx (not .xls)\n• Removing password protection`);
                return;
            }

            const options = fallbackOptions[optionIndex];
            logDebug('info', `Trying Excel parsing option ${optionIndex + 1}/${fallbackOptions.length}`, options);

            try {
                // Convert array buffer for binary option
                let dataToRead = arrayBuffer;
                if (options.type === 'binary') {
                    const uint8Array = new Uint8Array(arrayBuffer);
                    dataToRead = String.fromCharCode.apply(null, uint8Array);
                }

                const workbook = XLSX.read(dataToRead, options);

                if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                    throw new Error('No worksheets found');
                }

                logDebug('success', `Excel parsing successful with option ${optionIndex + 1}`, {
                    sheetNames: workbook.SheetNames,
                    sheetCount: workbook.SheetNames.length
                });

                // Find worksheet with data using enhanced detection
                let worksheet = null;
                let sheetName = null;

                // Try each sheet
                for (const name of workbook.SheetNames) {
                    const sheet = workbook.Sheets[name];
                    if (sheet) {
                        // Try to convert a small sample to test for data
                        try {
                            const testData = XLSX.utils.sheet_to_json(sheet, {
                                header: 1,
                                defval: '',
                                blankrows: false,
                                range: 0 // Only first few rows for testing
                            });

                            if (testData && testData.length > 0) {
                                // Check if there's actual content (not just empty cells)
                                const hasContent = testData.some(row =>
                                    Array.isArray(row) && row.some(cell =>
                                        cell !== null && cell !== undefined && cell !== ''
                                    )
                                );

                                if (hasContent) {
                                    worksheet = sheet;
                                    sheetName = name;
                                    logDebug('info', `Found data in worksheet: "${name}"`, {
                                        testRowCount: testData.length,
                                        sampleData: testData[0]
                                    });
                                    break;
                                }
                            }
                        } catch (testError) {
                            logDebug('warn', `Test conversion failed for sheet "${name}": ${testError.message}`);
                        }
                    }
                }

                if (!worksheet) {
                    throw new Error('No worksheets contain readable data');
                }

                // Success! Continue with streaming conversion
                updateProgressIndicator('parse', 90, `Processing data from sheet: ${sheetName}`);

                // Clear array buffer
                arrayBuffer = null;

                setTimeout(() => {
                    processExcelStreamingConvert(worksheet, fileName);
                }, 50);

            } catch (optionError) {
                logDebug('warn', `Excel parsing option ${optionIndex + 1} failed: ${optionError.message}`);
                optionIndex++;
                setTimeout(tryNextOption, 100);
            }
        }

        // Start trying options
        tryNextOption();

    } catch (error) {
        logDebug('error', 'Excel fallback parsing error', {
            error: error.message,
            stack: error.stack
        });
        hideProgressIndicators();
        showErrorMessageWithDebug('Error in Excel fallback parsing: ' + error.message);
    }
}

function handleAnalysisComplete(results) {
    try {
        console.log('Handling analysis completion...', results);

        // Clear any processing timeout
        clearProcessingTimeout();

        analysisResults = results;
        // Keep currentData available for export functions
        // Note: For very large datasets, consider implementing streaming export

        // Update UI
        console.log('Updating statistics...');
        updateStatistics();

        console.log('Updating field analysis table...');
        updateFieldAnalysisTable();

        console.log('Creating charts...');
        createCharts();

        console.log('Creating risk assessment...');
        createRiskAssessment();

        // Hide progress and show results
        hideProgressIndicators();

        const analysisSection = document.getElementById('analysisSection');
        if (analysisSection) {
            analysisSection.style.display = 'block';
            console.log('Analysis section displayed');

            // Scroll to results
            analysisSection.scrollIntoView({
                behavior: 'smooth'
            });
        } else {
            console.error('Analysis section not found');
        }

        console.log('Analysis complete and results displayed successfully');

    } catch (error) {
        console.error('Error in handleAnalysisComplete:', error);
        hideProgressIndicators();
        showErrorMessage('Error displaying results: ' + error.message);
    }
}

function displayFileInfo(file) {
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileInfo').style.display = 'block';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function processCSV(file) {
    console.log('Processing CSV file:', file.name, 'Size:', formatFileSize(file.size));
    updateProgressIndicator('parse', 10, 'Preparing to read CSV file...');

    // For very large files, use streaming approach
    if (file.size > 50 * 1024 * 1024) { // > 50MB
        console.log('Large file detected, using streaming approach');
        processLargeCSV(file);
    } else {
        console.log('Standard file size, using normal approach');
        processStandardCSV(file);
    }
}

function processStandardCSV(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        console.log('CSV file loaded into memory');
        updateProgressIndicator('parse', 50, 'Parsing CSV data...');

        const csv = e.target.result;
        const lines = csv.split('\n');
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

        const data = [];
        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = parseCSVLine(lines[i]);
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index] || '';
                });
                data.push(row);
            }
        }

        currentData = data;
        document.getElementById('recordCount').textContent = data.length;
        updateProgressIndicator('parse', 100, 'CSV parsing complete');

        setTimeout(() => {
            analyzeDataProgressive();
        }, 100);
    };
    reader.readAsText(file);
}

function processLargeCSV(file) {
    logDebug('info', 'Starting streaming CSV processing', {
        fileName: file.name,
        fileSize: file.size,
        chunkSize: '1MB'
    });
    updateProgressIndicator('parse', 20, 'Reading large CSV file in chunks...');

    const chunkSize = 1024 * 1024; // 1MB chunks
    let offset = 0;
    let csvData = '';
    let processedRows = 0;
    let headers = null;
    const allData = [];

    function readChunk() {
        const slice = file.slice(offset, offset + chunkSize);
        const reader = new FileReader();

        reader.onload = function(e) {
            const chunk = e.target.result;
            csvData += chunk;

            // Process complete lines
            const lines = csvData.split('\n');
            const completeLines = lines.slice(0, -1); // All but the last (potentially incomplete) line
            csvData = lines[lines.length - 1]; // Keep the last incomplete line for next chunk

            // Process the complete lines
            for (let i = 0; i < completeLines.length; i++) {
                const line = completeLines[i].trim();
                if (!line) continue;

                if (!headers) {
                    headers = line.split(',').map(h => h.trim().replace(/"/g, ''));
                    console.log('Headers found:', headers);
                    continue;
                }

                const row = parseCSVLine(line);
                if (row.length === headers.length) {
                    const rowData = {};
                    headers.forEach((header, index) => {
                        rowData[header] = row[index] || '';
                    });
                    allData.push(rowData);
                    processedRows++;
                }

                // Update progress periodically
                if (processedRows % 5000 === 0) {
                    const progress = Math.min(90, 20 + (offset / file.size) * 70);
                    updateProgressIndicator('parse', progress, `Processed ${processedRows.toLocaleString()} rows...`);
                    console.log(`CSV parsing progress: ${processedRows.toLocaleString()} rows processed, ${Math.round(progress)}% complete`);
                }
            }

            offset += chunkSize;

            if (offset < file.size) {
                // Continue reading next chunk
                // Use requestAnimationFrame for better performance
                if (window.requestAnimationFrame) {
                    requestAnimationFrame(readChunk);
                } else {
                    setTimeout(readChunk, 1); // Minimal delay
                }
            } else {
                // Process any remaining data
                if (csvData.trim()) {
                    const row = parseCSVLine(csvData.trim());
                    if (headers && row.length === headers.length) {
                        const rowData = {};
                        headers.forEach((header, index) => {
                            rowData[header] = row[index] || '';
                        });
                        allData.push(rowData);
                        processedRows++;
                    }
                }

                logDebug('success', `CSV parsing complete. Processed ${processedRows} rows`, {
                    totalRows: processedRows,
                    fileSize: file.size,
                    processingTime: Date.now() - processingStartTime
                });
                updateProgressIndicator('parse', 100, `Parsed ${processedRows.toLocaleString()} rows`);

                // Store data and start analysis
                currentData = allData;
                const recordCountEl = document.getElementById('recordCount');
                if (recordCountEl) recordCountEl.textContent = allData.length.toLocaleString();

                setTimeout(() => {
                    analyzeDataProgressive();
                }, 100);
            }
        };

        reader.onerror = function(error) {
            logDebug('error', 'Error reading CSV file chunk', {
                error: error,
                offset: offset,
                chunkSize: chunkSize
            });
            showErrorMessageWithDebug('Error reading file. The file may be corrupted or too large.');
        };

        reader.readAsText(slice);
    }

    readChunk();
}

function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }
    result.push(current.trim());
    return result;
}

function processExcel(file) {
    console.log('Processing Excel file:', file.name, 'Size:', formatFileSize(file.size));
    updateProgressIndicator('parse', 10, 'Preparing to read Excel file...');

    // Check file size and warn user
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > 100) {
        showErrorMessage('Excel file is very large (' + Math.round(fileSizeMB) + 'MB). For better performance, please convert to CSV format. Large Excel files may cause memory issues.');
        return;
    } else if (fileSizeMB > 50) {
        console.warn('Large Excel file detected:', Math.round(fileSizeMB) + 'MB');
        updateProgressIndicator('parse', 15, 'Large Excel file detected, using optimized processing...');
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            console.log('Excel file loaded into memory, parsing...');
            updateProgressIndicator('parse', 50, 'Parsing Excel data...');

            const data = new Uint8Array(e.target.result);

            // Use memory-efficient options for large files
            const readOptions = {
                type: 'array',
                cellDates: false,
                cellNF: false,
                cellStyles: false,
                sheetStubs: false,
                bookDeps: false,
                bookFiles: false,
                bookProps: false,
                bookSheets: false,
                bookVBA: false
            };

            if (fileSizeMB > 50) {
                readOptions.dense = true; // Use dense mode for large files
                console.log('Using dense mode for large Excel file');
            }

            const workbook = XLSX.read(data, readOptions);
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];

            console.log('Converting Excel sheet to JSON...');
            updateProgressIndicator('parse', 80, 'Converting Excel data...');

            // Convert to JSON with memory-efficient options
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                defval: '', // Default value for empty cells
                blankrows: false // Skip blank rows
            });

            console.log(`Excel parsing complete. Processed ${jsonData.length} rows`);
            updateProgressIndicator('parse', 100, `Parsed ${jsonData.length.toLocaleString()} rows`);

            currentData = jsonData;
            const recordCountEl = document.getElementById('recordCount');
            if (recordCountEl) recordCountEl.textContent = jsonData.length.toLocaleString();

            setTimeout(() => {
                analyzeDataProgressive();
            }, 100);

        } catch (error) {
            console.error('Excel processing error:', error);
            if (error.message.includes('Invalid array length') || error.message.includes('out of memory')) {
                showErrorMessage('Excel file is too large to process in browser memory. Please convert to CSV format or use a smaller file.');
            } else {
                showErrorMessage('Error processing Excel file: ' + error.message + '. Please try converting to CSV format.');
            }
        }
    };

    reader.onerror = function() {
        console.error('Error reading Excel file');
        showErrorMessage('Error reading Excel file. The file may be corrupted.');
    };

    reader.readAsArrayBuffer(file);
}

function analyzeData() {
    // Redirect to progressive analysis for better performance
    analyzeDataProgressive();
}

function analyzeDataProgressive() {
    logDebug('info', 'Starting progressive data analysis', {
        dataLength: currentData ? currentData.length : 0,
        memoryUsage: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB' : 'N/A'
    });
    updateProgressIndicator('analyze', 10, 'Analyzing data patterns...');

    if (!currentData || currentData.length === 0) {
        logDebug('error', 'No data to analyze - currentData is empty or null');
        showErrorMessage('No data available for analysis. Please upload a valid file.');
        return;
    }

    const availableFields = Object.keys(currentData[0]);
    const totalRecords = currentData.length;
    console.log(`Analyzing ${totalRecords.toLocaleString()} records with ${availableFields.length} fields`);

    // Initialize analysis results
    const fieldAnalysis = {};
    const profileCompleteness = [];
    let completeProfiles = 0;
    let incompleteProfiles = 0;

    // Initialize field analysis
    availableFields.forEach(field => {
        fieldAnalysis[field] = {
            totalRecords,
            blankCount: 0,
            blankPercentage: 0,
            riskLevel: 'LOW'
        };
    });

    // Initialize address group analysis
    fieldAnalysis['ADDRESS_GROUP'] = {
        totalRecords,
        blankCount: 0,
        blankPercentage: 0,
        riskLevel: 'LOW'
    };

    // Initialize address field analysis
    const addressAnalysis = {
        totalRecords,
        addressFieldStats: {},
        addressDistribution: {},
        addressCompletionPatterns: {}
    };

    // Initialize stats for each address field
    addressFields.forEach(field => {
        addressAnalysis.addressFieldStats[field] = {
            populatedCount: 0,
            populatedPercentage: 0
        };
    });

    // Process data in chunks to prevent UI blocking
    // Larger chunk sizes for better performance with very large files
    const chunkSize = Math.min(10000, Math.max(500, Math.floor(totalRecords / 100))); // Adaptive chunk size
    let processedRecords = 0;

    console.log(`Processing ${totalRecords.toLocaleString()} records in chunks of ${chunkSize.toLocaleString()}`);

    function processChunk() {
        const endIndex = Math.min(processedRecords + chunkSize, totalRecords);

        // Process this chunk
        for (let i = processedRecords; i < endIndex; i++) {
            const row = currentData[i];
            let filledFields = 0;

            // First, analyze address group requirement
            let hasAnyAddress = false;
            const recordAddressPattern = [];

            addressFields.forEach(field => {
                const hasValue = !isFieldValueBlank(field, row[field], row, availableFields);

                if (hasValue) {
                    hasAnyAddress = true;
                    addressAnalysis.addressFieldStats[field].populatedCount++;
                    recordAddressPattern.push(field);
                }
            });

            // Analyze each field with address group validation and special field rules
            availableFields.forEach(field => {
                const isEmpty = isFieldValueBlank(field, row[field], row, availableFields);

                // Apply address group validation for field analysis
                if (addressFields.includes(field)) {
                    // For address fields, only count as blank if NO address exists
                    if (!hasAnyAddress) {
                        fieldAnalysis[field].blankCount++;
                    }
                    // Count individual field completion for filledFields counter
                    if (!isEmpty) {
                        filledFields++;
                    }
                } else {
                    // For non-address fields, count normally with special validation
                    if (isEmpty) {
                        fieldAnalysis[field].blankCount++;
                    } else {
                        filledFields++;
                    }
                }
            });

            // Mark address group as complete if any address field is populated
            if (!hasAnyAddress) {
                fieldAnalysis['ADDRESS_GROUP'].blankCount++;
            }

            // Track address completion patterns
            const patternKey = recordAddressPattern.sort().join(',') || 'NO_ADDRESS';
            addressAnalysis.addressCompletionPatterns[patternKey] =
                (addressAnalysis.addressCompletionPatterns[patternKey] || 0) + 1;

            // Calculate profile completeness using address group validation
            // Count individual address fields as filled if ANY address exists
            let adjustedFilledFields = filledFields;

            // If any address field is populated, count all address fields as filled
            if (hasAnyAddress) {
                // Add missing address fields to the filled count
                addressFields.forEach(field => {
                    if (availableFields.includes(field) && isFieldValueBlank(field, row[field], row, availableFields)) {
                        adjustedFilledFields++;
                    }
                });
            }

            const completeness = (adjustedFilledFields / availableFields.length) * 100;
            profileCompleteness.push(completeness);

            if (completeness === 100) {
                completeProfiles++;
            } else {
                incompleteProfiles++;
            }
        }

        processedRecords = endIndex;

        // Update progress
        const progress = Math.min(90, 10 + (processedRecords / totalRecords) * 80);
        updateProgressIndicator('analyze', progress, `Analyzed ${processedRecords.toLocaleString()} of ${totalRecords.toLocaleString()} records...`);

        // Log progress every 50,000 records
        if (processedRecords % 50000 === 0 || processedRecords === totalRecords) {
            console.log(`Analysis progress: ${processedRecords.toLocaleString()}/${totalRecords.toLocaleString()} records (${Math.round(progress)}%)`);
        }

        if (processedRecords < totalRecords) {
            // Continue processing next chunk
            // Use requestAnimationFrame for better performance, fallback to setTimeout
            if (window.requestAnimationFrame) {
                requestAnimationFrame(processChunk);
            } else {
                setTimeout(processChunk, 1); // Minimal delay
            }
        } else {
            // Finalize analysis
            finalizeAnalysis();
        }
    }

    function finalizeAnalysis() {
        console.log('Finalizing analysis...');

        // Calculate final field statistics
        availableFields.forEach(field => {
            const fieldData = fieldAnalysis[field];
            fieldData.blankPercentage = parseFloat(((fieldData.blankCount / totalRecords) * 100).toFixed(2));
            fieldData.riskLevel = getRiskLevel(field, fieldData.blankPercentage);
        });

        // Calculate address group statistics
        const addressGroupData = fieldAnalysis['ADDRESS_GROUP'];
        addressGroupData.blankPercentage = parseFloat(((addressGroupData.blankCount / totalRecords) * 100).toFixed(2));
        addressGroupData.riskLevel = getRiskLevel('ADDRESS_GROUP', addressGroupData.blankPercentage);

        // Calculate address field percentages
        addressFields.forEach(field => {
            const stats = addressAnalysis.addressFieldStats[field];
            stats.populatedPercentage = parseFloat(((stats.populatedCount / totalRecords) * 100).toFixed(2));
        });

        // Calculate address distribution
        const totalWithAddress = totalRecords - addressGroupData.blankCount;
        addressAnalysis.addressDistribution = {
            withAddress: totalWithAddress,
            withAddressPercentage: parseFloat(((totalWithAddress / totalRecords) * 100).toFixed(2)),
            withoutAddress: addressGroupData.blankCount,
            withoutAddressPercentage: addressGroupData.blankPercentage
        };

        // Recalculate complete/incomplete profiles from profileCompleteness array
        const actualCompleteProfiles = profileCompleteness.filter(p => p === 100).length;
        const actualIncompleteProfiles = totalRecords - actualCompleteProfiles;
        const completionRate = parseFloat(((actualCompleteProfiles / totalRecords) * 100).toFixed(1));

        // Update the counts with correct values
        completeProfiles = actualCompleteProfiles;
        incompleteProfiles = actualIncompleteProfiles;

        // Store results
        analysisResults = {
            fieldAnalysis,
            profileCompleteness,
            totalRecords,
            completeProfiles,
            incompleteProfiles,
            completionRate,
            availableFields,
            addressAnalysis
        };

        console.log('Analysis complete:', analysisResults);
        updateProgressIndicator('analyze', 100, 'Analysis complete');

        // Clear processing timeout
        clearProcessingTimeout();

        // Update UI
        updateStatistics();
        updateFieldAnalysisTable();
        createCharts();
        createRiskAssessment();

        // Show results
        hideProgressIndicators();
        document.getElementById('analysisSection').style.display = 'block';
    }

    // Start processing
    processChunk();
}

function getRiskLevel(field, blankPercentage) {
    const isCritical = criticalFields.includes(field);

    if (isCritical) {
        if (blankPercentage > 10) return 'HIGH';
        if (blankPercentage > 5) return 'MEDIUM';
        return 'LOW';
    } else {
        if (blankPercentage > 25) return 'HIGH';
        if (blankPercentage > 15) return 'MEDIUM';
        return 'LOW';
    }
}

function updateStatistics() {
    // Add null checks to prevent errors
    const totalRecordsEl = document.getElementById('totalRecords');
    const completeProfilesEl = document.getElementById('completeProfiles');
    const incompleteProfilesEl = document.getElementById('incompleteProfiles');
    const completionRateEl = document.getElementById('completionRate');

    if (totalRecordsEl) totalRecordsEl.textContent = analysisResults.totalRecords.toLocaleString();
    if (completeProfilesEl) completeProfilesEl.textContent = analysisResults.completeProfiles.toLocaleString();
    if (incompleteProfilesEl) incompleteProfilesEl.textContent = analysisResults.incompleteProfiles.toLocaleString();
    if (completionRateEl) completionRateEl.textContent = analysisResults.completionRate + '%';
}

function updateFieldAnalysisTable() {
    // Initialize filtered data
    filteredData = Object.entries(analysisResults.fieldAnalysis)
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage);

    // Update the table with pagination
    updateTable();
}

function createCharts() {
    createMissingDataChart();
    createCompletenessChart();
    createAddressAnalysisChart();
}

function createMissingDataChart() {
    const ctx = document.getElementById('missingDataChart').getContext('2d');

    if (missingDataChart) {
        missingDataChart.destroy();
    }

    const fieldData = Object.entries(analysisResults.fieldAnalysis)
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
        .slice(0, 10); // Top 10 fields with most missing data

    const labels = fieldData.map(([field]) => field);
    const data = fieldData.map(([, analysis]) => parseFloat(analysis.blankPercentage));
    const colors = fieldData.map(([field, analysis]) => {
        if (analysis.riskLevel === 'HIGH') return '#ff6b6b';
        if (analysis.riskLevel === 'MEDIUM') return '#ffa726';
        return '#66bb6a';
    });

    missingDataChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Missing Data %',
                data: data,
                backgroundColor: colors,
                borderColor: colors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function createCompletenessChart() {
    const ctx = document.getElementById('completenessChart').getContext('2d');

    if (completenessChart) {
        completenessChart.destroy();
    }

    // Safety check for profileCompleteness
    if (!analysisResults || !analysisResults.profileCompleteness) {
        console.warn('Cannot create completeness chart: profileCompleteness data not available');
        return;
    }

    // Create completeness distribution
    const ranges = [
        { label: '0-25%', min: 0, max: 25, color: '#ff6b6b' },
        { label: '26-50%', min: 26, max: 50, color: '#ffa726' },
        { label: '51-75%', min: 51, max: 75, color: '#ffca28' },
        { label: '76-99%', min: 76, max: 99, color: '#66bb6a' },
        { label: '100%', min: 100, max: 100, color: '#4caf50' }
    ];

    const distribution = ranges.map(range => {
        return analysisResults.profileCompleteness.filter(completeness =>
            completeness >= range.min && completeness <= range.max
        ).length;
    });

    completenessChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ranges.map(r => r.label),
            datasets: [{
                data: distribution,
                backgroundColor: ranges.map(r => r.color),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function createAddressAnalysisChart() {
    if (!analysisResults.addressAnalysis) return;

    const ctx = document.getElementById('addressChart');
    if (!ctx) return;

    const addressStats = analysisResults.addressAnalysis.addressFieldStats;

    // Destroy existing chart if it exists
    if (window.addressChart instanceof Chart) {
        window.addressChart.destroy();
    }

    const labels = Object.keys(addressStats).map(field =>
        field.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
    );
    const data = Object.values(addressStats).map(stats => stats.populatedPercentage);
    const colors = ['#1976d2', '#388e3c', '#f57c00', '#7b1fa2'];

    window.addressChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Address Field Completion Rate (%)',
                data: data,
                backgroundColor: colors,
                borderColor: colors.map(color => color + '80'),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Address Field Completion Analysis'
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

function createRiskAssessment() {
    const riskGrid = document.getElementById('riskGrid');
    riskGrid.innerHTML = '';

    // Risk Overview Section
    createRiskOverviewSection(riskGrid);

    // Priority Matrix Section
    createPriorityMatrixSection(riskGrid);

    // Remediation Strategy Section
    createRemediationStrategySection(riskGrid);

    // Regulatory Impact Section
    createRegulatoryImpactSection(riskGrid);

    // Branch Assignment Strategy Section
    createBranchAssignmentSection(riskGrid);

    // Monitoring Framework Section
    createMonitoringFrameworkSection(riskGrid);
}

function createRiskOverviewSection(riskGrid) {
    const highRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'HIGH');
    const mediumRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'MEDIUM');
    const lowRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'LOW');

    const overallRiskLevel = highRiskFields.length > 5 ? 'CRITICAL' :
                            highRiskFields.length > 2 ? 'HIGH' :
                            mediumRiskFields.length > 5 ? 'MEDIUM' : 'LOW';

    const riskOverviewCard = document.createElement('div');
    riskOverviewCard.className = 'risk-card';
    riskOverviewCard.style.borderLeftColor = overallRiskLevel === 'CRITICAL' ? '#d32f2f' :
                                            overallRiskLevel === 'HIGH' ? '#f57c00' :
                                            overallRiskLevel === 'MEDIUM' ? '#fbc02d' : '#388e3c';
    riskOverviewCard.innerHTML = `
        <h3 style="color: #1976d2; margin-bottom: 15px;">🎯 AML Risk Assessment Overview</h3>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
            <h4 style="color: #333; margin-bottom: 10px;">Overall Risk Level:
                <span class="risk-badge risk-${overallRiskLevel.toLowerCase()}" style="font-size: 1.1em;">${overallRiskLevel}</span>
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div style="text-align: center; padding: 10px; background: #ffebee; border-radius: 6px;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #c62828;">${highRiskFields.length}</div>
                    <div style="color: #666; font-size: 0.9em;">Critical Fields</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #fff3e0; border-radius: 6px;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #f57c00;">${mediumRiskFields.length}</div>
                    <div style="color: #666; font-size: 0.9em;">Medium Risk Fields</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #e8f5e8; border-radius: 6px;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #388e3c;">${lowRiskFields.length}</div>
                    <div style="color: #666; font-size: 0.9em;">Low Risk Fields</div>
                </div>
            </div>
        </div>

        ${highRiskFields.length > 0 ? `
        <div style="background: #ffebee; padding: 15px; border-radius: 8px; border-left: 4px solid #c62828;">
            <h4 style="color: #c62828; margin-bottom: 10px;">🚨 Critical Compliance Gaps</h4>
            <p style="margin-bottom: 10px;"><strong>Immediate Action Required:</strong> ${highRiskFields.length} fields pose significant AML compliance risks</p>
            <ul style="margin: 0; padding-left: 20px;">
                ${highRiskFields.slice(0, 5).map(([field, analysis]) =>
                    `<li style="margin: 5px 0;"><strong>${field}</strong>: ${analysis.blankPercentage}% missing (${analysis.blankCount.toLocaleString()} records)</li>`
                ).join('')}
                ${highRiskFields.length > 5 ? `<li style="margin: 5px 0; color: #666;"><em>... and ${highRiskFields.length - 5} more critical fields</em></li>` : ''}
            </ul>
        </div>
        ` : ''}
    `;
    riskGrid.appendChild(riskOverviewCard);
}

function createPriorityMatrixSection(riskGrid) {
    const criticalFieldsAnalysis = criticalFields
        .filter(field => analysisResults.fieldAnalysis[field])
        .map(field => ({
            field,
            ...analysisResults.fieldAnalysis[field]
        }));

    const priorityCard = document.createElement('div');
    priorityCard.className = 'risk-card';
    priorityCard.style.borderLeftColor = '#1976d2';
    priorityCard.innerHTML = `
        <h3 style="color: #1976d2; margin-bottom: 15px;">📋 AML Compliance Priority Matrix</h3>

        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
            <h4 style="color: #1976d2; margin-bottom: 10px;">🔍 Critical AML Fields Assessment</h4>
            <div style="display: grid; gap: 10px;">
                ${criticalFieldsAnalysis.map(analysis => {
                    const urgency = analysis.blankPercentage > 50 ? 'IMMEDIATE' :
                                   analysis.blankPercentage > 25 ? '30 DAYS' : '90 DAYS';
                    const urgencyColor = urgency === 'IMMEDIATE' ? '#d32f2f' :
                                        urgency === '30 DAYS' ? '#f57c00' : '#388e3c';
                    return `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: white; border-radius: 6px; border-left: 3px solid ${urgencyColor};">
                        <div>
                            <strong>${analysis.field}</strong>
                            <div style="font-size: 0.9em; color: #666;">${analysis.blankPercentage}% missing (${analysis.blankCount.toLocaleString()} records)</div>
                        </div>
                        <div style="text-align: right;">
                            <span class="risk-badge risk-${analysis.riskLevel.toLowerCase()}">${analysis.riskLevel}</span>
                            <div style="font-size: 0.8em; color: ${urgencyColor}; font-weight: bold; margin-top: 2px;">${urgency}</div>
                        </div>
                    </div>
                    `;
                }).join('')}
            </div>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
            <div style="background: #ffebee; padding: 15px; border-radius: 8px; border-left: 4px solid #d32f2f;">
                <h4 style="color: #d32f2f; margin-bottom: 10px;">🚨 IMMEDIATE (0-7 Days)</h4>
                <p style="font-size: 0.9em; margin-bottom: 8px;"><strong>Fields with >50% missing data</strong></p>
                <p style="font-size: 0.85em; color: #666;">Critical regulatory compliance risk. Requires immediate senior management attention and emergency remediation protocols.</p>
            </div>
            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; border-left: 4px solid #f57c00;">
                <h4 style="color: #f57c00; margin-bottom: 10px;">⚠️ HIGH PRIORITY (30 Days)</h4>
                <p style="font-size: 0.9em; margin-bottom: 8px;"><strong>Fields with 25-50% missing data</strong></p>
                <p style="font-size: 0.85em; color: #666;">Significant compliance gaps. Requires structured remediation plan with weekly progress reviews.</p>
            </div>
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #388e3c;">
                <h4 style="color: #388e3c; margin-bottom: 10px;">✅ STANDARD (90 Days)</h4>
                <p style="font-size: 0.9em; margin-bottom: 8px;"><strong>Fields with <25% missing data</strong></p>
                <p style="font-size: 0.85em; color: #666;">Manageable gaps. Can be addressed through regular operational processes with monthly monitoring.</p>
            </div>
        </div>
    `;
    riskGrid.appendChild(priorityCard);
}

function createRemediationStrategySection(riskGrid) {
    const highRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'HIGH');
    const mediumRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'MEDIUM');

    const remediationCard = document.createElement('div');
    remediationCard.className = 'risk-card';
    remediationCard.style.borderLeftColor = '#7b1fa2';
    remediationCard.innerHTML = `
        <h3 style="color: #7b1fa2; margin-bottom: 15px;">🎯 KYC Remediation Action Plan</h3>

        <div style="display: grid; gap: 15px;">
            <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; border-left: 4px solid #7b1fa2;">
                <h4 style="color: #7b1fa2; margin-bottom: 10px;">📋 Immediate Action Items</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li style="margin: 8px 0;"><strong>Escalate to Senior Management:</strong> ${highRiskFields.length} critical fields require executive attention</li>
                    <li style="margin: 8px 0;"><strong>Activate Emergency Protocols:</strong> Deploy additional resources for high-risk field completion</li>
                    <li style="margin: 8px 0;"><strong>Regulatory Notification:</strong> Prepare compliance status report for regulatory authorities</li>
                    <li style="margin: 8px 0;"><strong>Customer Outreach:</strong> Initiate immediate contact for incomplete high-risk profiles</li>
                    <li style="margin: 8px 0;"><strong>System Flags:</strong> Mark incomplete profiles for enhanced monitoring and transaction restrictions</li>
                </ul>
            </div>

            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #388e3c;">
                <h4 style="color: #388e3c; margin-bottom: 10px;">🔄 Systematic Remediation Approach</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    <div>
                        <h5 style="color: #2e7d32; margin-bottom: 8px;">Phase 1: Critical Fields (0-30 days)</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li>NATIONAL_IDENTIFIER, DATE_OF_BIRTH</li>
                            <li>FULL_NAME, CONTACT_NO</li>
                            <li>Address verification (any address type)</li>
                            <li>Occupation and income source</li>
                        </ul>
                    </div>
                    <div>
                        <h5 style="color: #2e7d32; margin-bottom: 8px;">Phase 2: Supporting Fields (30-60 days)</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li>NO_OF_PTO, NO_OF_SIGNATURE validation</li>
                            <li>Secondary contact information</li>
                            <li>Additional address types</li>
                            <li>Enhanced due diligence fields</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; border-left: 4px solid #f57c00;">
                <h4 style="color: #f57c00; margin-bottom: 10px;">⚖️ Risk Categorization & Regulatory Impact</h4>
                <div style="display: grid; gap: 10px;">
                    <div style="padding: 10px; background: white; border-radius: 6px;">
                        <strong style="color: #d32f2f;">Critical AML Fields:</strong>
                        <span style="font-size: 0.9em; color: #666;">NATIONAL_IDENTIFIER, FULL_NAME, DATE_OF_BIRTH, ADDRESS - Required for CDD compliance, failure results in regulatory sanctions</span>
                    </div>
                    <div style="padding: 10px; background: white; border-radius: 6px;">
                        <strong style="color: #f57c00;">Enhanced Due Diligence:</strong>
                        <span style="font-size: 0.9em; color: #666;">OCCUPATION, CONTACT_NO, NO_OF_PTO - Required for risk assessment, impacts transaction monitoring effectiveness</span>
                    </div>
                    <div style="padding: 10px; background: white; border-radius: 6px;">
                        <strong style="color: #388e3c;">Supporting Documentation:</strong>
                        <span style="font-size: 0.9em; color: #666;">NO_OF_SIGNATURE, DEFAULT - Quality indicators, affects audit findings but not immediate compliance risk</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    riskGrid.appendChild(remediationCard);
}

function createRegulatoryImpactSection(riskGrid) {
    const complianceLevel = analysisResults.completionRate >= 90 ? 'EXCELLENT' :
                           analysisResults.completionRate >= 75 ? 'GOOD' :
                           analysisResults.completionRate >= 60 ? 'MODERATE' : 'POOR';

    const regulatoryCard = document.createElement('div');
    regulatoryCard.className = 'risk-card';
    regulatoryCard.style.borderLeftColor = '#1565c0';
    regulatoryCard.innerHTML = `
        <h3 style="color: #1565c0; margin-bottom: 15px;">⚖️ Regulatory Compliance Impact Assessment</h3>

        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
            <h4 style="color: #1565c0; margin-bottom: 10px;">📊 Overall Compliance Status</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; border-left: 4px solid ${complianceLevel === 'EXCELLENT' ? '#388e3c' : complianceLevel === 'GOOD' ? '#1976d2' : complianceLevel === 'MODERATE' ? '#f57c00' : '#d32f2f'};">
                    <div style="font-size: 2.2em; font-weight: bold; color: ${complianceLevel === 'EXCELLENT' ? '#388e3c' : complianceLevel === 'GOOD' ? '#1976d2' : complianceLevel === 'MODERATE' ? '#f57c00' : '#d32f2f'};">${analysisResults.completionRate}%</div>
                    <div style="color: #666; font-size: 0.9em; margin-bottom: 5px;">Profile Completion Rate</div>
                    <span class="risk-badge" style="background: ${complianceLevel === 'EXCELLENT' ? '#388e3c' : complianceLevel === 'GOOD' ? '#1976d2' : complianceLevel === 'MODERATE' ? '#f57c00' : '#d32f2f'}; color: white;">${complianceLevel}</span>
                </div>
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #388e3c;">${analysisResults.completeProfiles.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Compliant Profiles</div>
                </div>
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #d32f2f;">${analysisResults.incompleteProfiles.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Non-Compliant Profiles</div>
                </div>
            </div>
        </div>

        <div style="display: grid; gap: 15px;">
            <div style="background: ${complianceLevel === 'POOR' ? '#ffebee' : '#f8f9fa'}; padding: 15px; border-radius: 8px; border-left: 4px solid ${complianceLevel === 'POOR' ? '#d32f2f' : '#1976d2'};">
                <h4 style="color: ${complianceLevel === 'POOR' ? '#d32f2f' : '#1976d2'}; margin-bottom: 10px;">🏛️ Regulatory Exposure Analysis</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                    <div>
                        <strong style="color: #d32f2f;">BSA/AML Compliance Risk:</strong>
                        <p style="font-size: 0.9em; margin: 5px 0; color: #666;">
                            ${complianceLevel === 'POOR' ? 'HIGH - Significant regulatory sanctions risk' :
                              complianceLevel === 'MODERATE' ? 'MEDIUM - Potential examination findings' :
                              'LOW - Meets regulatory expectations'}
                        </p>
                    </div>
                    <div>
                        <strong style="color: #f57c00;">CDD Requirements:</strong>
                        <p style="font-size: 0.9em; margin: 5px 0; color: #666;">
                            ${analysisResults.completionRate < 75 ? 'Non-compliant with CDD rule requirements' :
                              'Generally compliant with enhanced monitoring needed'}
                        </p>
                    </div>
                    <div>
                        <strong style="color: #1976d2;">Examination Readiness:</strong>
                        <p style="font-size: 0.9em; margin: 5px 0; color: #666;">
                            ${complianceLevel === 'EXCELLENT' ? 'Examination ready' :
                              complianceLevel === 'GOOD' ? 'Minor remediation needed' :
                              'Significant preparation required'}
                        </p>
                    </div>
                </div>
            </div>

            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; border-left: 4px solid #f57c00;">
                <h4 style="color: #f57c00; margin-bottom: 10px;">📋 Compliance Recommendations</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    ${complianceLevel === 'POOR' ? `
                    <li style="margin: 8px 0;"><strong>Immediate:</strong> Implement emergency remediation protocols and notify board of directors</li>
                    <li style="margin: 8px 0;"><strong>Regulatory:</strong> Consider voluntary disclosure to regulatory authorities</li>
                    <li style="margin: 8px 0;"><strong>Operations:</strong> Halt new account openings until compliance gaps are addressed</li>
                    ` : complianceLevel === 'MODERATE' ? `
                    <li style="margin: 8px 0;"><strong>Priority:</strong> Accelerate remediation efforts with additional resources</li>
                    <li style="margin: 8px 0;"><strong>Monitoring:</strong> Implement enhanced transaction monitoring for incomplete profiles</li>
                    <li style="margin: 8px 0;"><strong>Reporting:</strong> Increase compliance reporting frequency to senior management</li>
                    ` : `
                    <li style="margin: 8px 0;"><strong>Maintenance:</strong> Continue regular monitoring and quality assurance processes</li>
                    <li style="margin: 8px 0;"><strong>Enhancement:</strong> Focus on process improvements and automation opportunities</li>
                    <li style="margin: 8px 0;"><strong>Training:</strong> Maintain staff competency through ongoing AML training programs</li>
                    `}
                </ul>
            </div>
        </div>
    `;
    riskGrid.appendChild(regulatoryCard);
}

function createBranchAssignmentSection(riskGrid) {
    const totalIncompleteProfiles = analysisResults.incompleteProfiles;
    const highRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'HIGH');
    const mediumRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'MEDIUM');

    // Calculate estimated workload
    const criticalWorkload = highRiskFields.reduce((sum, [, analysis]) => sum + analysis.blankCount, 0);
    const standardWorkload = mediumRiskFields.reduce((sum, [, analysis]) => sum + analysis.blankCount, 0);

    const branchCard = document.createElement('div');
    branchCard.className = 'risk-card';
    branchCard.style.borderLeftColor = '#6a1b9a';
    branchCard.innerHTML = `
        <h3 style="color: #6a1b9a; margin-bottom: 15px;">🏢 Branch Assignment & Resource Allocation Strategy</h3>

        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
            <h4 style="color: #6a1b9a; margin-bottom: 10px;">📊 Workload Distribution Analysis</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; border-left: 4px solid #d32f2f;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #d32f2f;">${criticalWorkload.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Critical Remediation Items</div>
                    <div style="font-size: 0.8em; color: #d32f2f; margin-top: 5px;">Immediate Priority</div>
                </div>
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; border-left: 4px solid #f57c00;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #f57c00;">${standardWorkload.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Standard Remediation Items</div>
                    <div style="font-size: 0.8em; color: #f57c00; margin-top: 5px;">30-90 Day Timeline</div>
                </div>
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; border-left: 4px solid #6a1b9a;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #6a1b9a;">${totalIncompleteProfiles.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Total Profiles to Review</div>
                    <div style="font-size: 0.8em; color: #6a1b9a; margin-top: 5px;">All Branches</div>
                </div>
            </div>
        </div>

        <div style="display: grid; gap: 15px;">
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #388e3c;">
                <h4 style="color: #388e3c; margin-bottom: 10px;">🎯 Branch Assignment Methodology</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    <div>
                        <h5 style="color: #2e7d32; margin-bottom: 8px;">Tier 1 Branches (High-Risk Specialists)</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li>Handle profiles with >3 critical missing fields</li>
                            <li>Complex cases requiring enhanced due diligence</li>
                            <li>Regulatory-sensitive customer segments</li>
                            <li>Estimated capacity: 50-75 profiles per week</li>
                        </ul>
                    </div>
                    <div>
                        <h5 style="color: #2e7d32; margin-bottom: 8px;">Tier 2 Branches (Standard Processing)</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li>Profiles with 1-2 missing critical fields</li>
                            <li>Standard KYC documentation gaps</li>
                            <li>Regular customer maintenance</li>
                            <li>Estimated capacity: 100-150 profiles per week</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; border-left: 4px solid #f57c00;">
                <h4 style="color: #f57c00; margin-bottom: 10px;">⏱️ Completion Timeline & Resource Requirements</h4>
                <div style="display: grid; gap: 10px;">
                    <div style="padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #d32f2f;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong style="color: #d32f2f;">IMMEDIATE (0-7 Days)</strong>
                                <div style="font-size: 0.9em; color: #666;">Critical compliance gaps requiring emergency response</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-weight: bold; color: #d32f2f;">2-3 FTE Required</div>
                                <div style="font-size: 0.8em; color: #666;">Senior compliance officers</div>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #f57c00;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong style="color: #f57c00;">HIGH PRIORITY (30 Days)</strong>
                                <div style="font-size: 0.9em; color: #666;">Structured remediation with weekly progress reviews</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-weight: bold; color: #f57c00;">4-6 FTE Required</div>
                                <div style="font-size: 0.8em; color: #666;">Mixed experience levels</div>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #388e3c;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong style="color: #388e3c;">STANDARD (90 Days)</strong>
                                <div style="font-size: 0.9em; color: #666;">Regular operational processes with monthly monitoring</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-weight: bold; color: #388e3c;">6-8 FTE Required</div>
                                <div style="font-size: 0.8em; color: #666;">Standard branch staff</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    riskGrid.appendChild(branchCard);
}

function createMonitoringFrameworkSection(riskGrid) {
    const monitoringCard = document.createElement('div');
    monitoringCard.className = 'risk-card';
    monitoringCard.style.borderLeftColor = '#00695c';
    monitoringCard.innerHTML = `
        <h3 style="color: #00695c; margin-bottom: 15px;">📈 Monitoring Framework & Escalation Protocols</h3>

        <div style="display: grid; gap: 15px;">
            <div style="background: #e0f2f1; padding: 15px; border-radius: 8px; border-left: 4px solid #00695c;">
                <h4 style="color: #00695c; margin-bottom: 10px;">📊 Progress Tracking & KPIs</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div style="background: white; padding: 12px; border-radius: 6px;">
                        <h5 style="color: #00695c; margin-bottom: 8px;">Daily Metrics</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li>Profiles completed per branch</li>
                            <li>Critical field completion rate</li>
                            <li>Customer contact success rate</li>
                            <li>Documentation collection rate</li>
                        </ul>
                    </div>
                    <div style="background: white; padding: 12px; border-radius: 6px;">
                        <h5 style="color: #00695c; margin-bottom: 8px;">Weekly Reviews</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li>Branch performance comparison</li>
                            <li>Resource allocation effectiveness</li>
                            <li>Escalation case analysis</li>
                            <li>Timeline adherence assessment</li>
                        </ul>
                    </div>
                    <div style="background: white; padding: 12px; border-radius: 6px;">
                        <h5 style="color: #00695c; margin-bottom: 8px;">Monthly Reporting</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li>Overall completion percentage</li>
                            <li>Regulatory compliance status</li>
                            <li>Risk level trend analysis</li>
                            <li>Process improvement recommendations</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="background: #ffebee; padding: 15px; border-radius: 8px; border-left: 4px solid #d32f2f;">
                <h4 style="color: #d32f2f; margin-bottom: 10px;">🚨 Escalation Triggers & Response Protocols</h4>
                <div style="display: grid; gap: 12px;">
                    <div style="padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #d32f2f;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <strong style="color: #d32f2f;">LEVEL 1: Branch Manager</strong>
                            <span style="font-size: 0.8em; background: #d32f2f; color: white; padding: 2px 8px; border-radius: 12px;">IMMEDIATE</span>
                        </div>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px; color: #666;">
                            <li>Daily target missed by >20%</li>
                            <li>Critical field completion rate <50%</li>
                            <li>Customer contact failure rate >30%</li>
                        </ul>
                    </div>
                    <div style="padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #f57c00;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <strong style="color: #f57c00;">LEVEL 2: Regional Compliance</strong>
                            <span style="font-size: 0.8em; background: #f57c00; color: white; padding: 2px 8px; border-radius: 12px;">24 HOURS</span>
                        </div>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px; color: #666;">
                            <li>Weekly targets missed by >15%</li>
                            <li>Multiple branches underperforming</li>
                            <li>Resource reallocation required</li>
                        </ul>
                    </div>
                    <div style="padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #7b1fa2;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <strong style="color: #7b1fa2;">LEVEL 3: Senior Management</strong>
                            <span style="font-size: 0.8em; background: #7b1fa2; color: white; padding: 2px 8px; border-radius: 12px;">48 HOURS</span>
                        </div>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px; color: #666;">
                            <li>Monthly completion rate <75%</li>
                            <li>Regulatory deadline at risk</li>
                            <li>Systemic process failures identified</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #388e3c;">
                <h4 style="color: #388e3c; margin-bottom: 10px;">✅ Success Metrics & Quality Assurance</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    <div>
                        <h5 style="color: #2e7d32; margin-bottom: 8px;">Completion Quality Standards</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li><strong>95%</strong> accuracy rate for critical fields</li>
                            <li><strong>100%</strong> documentation verification</li>
                            <li><strong>90%</strong> customer contact success rate</li>
                            <li><strong>Zero</strong> regulatory compliance gaps</li>
                        </ul>
                    </div>
                    <div>
                        <h5 style="color: #2e7d32; margin-bottom: 8px;">Performance Benchmarks</h5>
                        <ul style="font-size: 0.9em; margin: 0; padding-left: 20px;">
                            <li><strong>Target:</strong> 15-20 profiles per FTE per day</li>
                            <li><strong>Quality:</strong> <5% rework rate</li>
                            <li><strong>Timeline:</strong> 100% adherence to priority schedules</li>
                            <li><strong>Customer:</strong> <24hr response time</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `;
    riskGrid.appendChild(monitoringCard);

    // Add Address Analysis if available
    if (analysisResults.addressAnalysis) {
        createAddressAnalysisSection(riskGrid);
    }
}

function createAddressAnalysisSection(riskGrid) {
    const addressStats = analysisResults.addressAnalysis.addressFieldStats;
    const addressDist = analysisResults.addressAnalysis.addressDistribution;

    // Find most common address type
    const mostCommonType = Object.entries(addressStats)
        .sort((a, b) => b[1].populatedCount - a[1].populatedCount)[0];

    // Calculate risk level based on address completion
    const addressRiskLevel = addressDist.withoutAddressPercentage > 10 ? 'HIGH' :
                            addressDist.withoutAddressPercentage > 5 ? 'MEDIUM' : 'LOW';

    const addressCard = document.createElement('div');
    addressCard.className = 'risk-card';
    addressCard.style.borderLeftColor = '#7b1fa2';
    addressCard.innerHTML = `
        <h3 style="color: #7b1fa2; margin-bottom: 15px;">🏠 Address Verification Analysis</h3>

        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
            <h4 style="color: #7b1fa2; margin-bottom: 10px;">📍 Address Completion Overview</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; border-left: 4px solid #388e3c;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #388e3c;">${addressDist.withAddress.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Records with Address</div>
                    <div style="font-size: 0.8em; color: #388e3c; margin-top: 5px;">${addressDist.withAddressPercentage}%</div>
                </div>
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; border-left: 4px solid #d32f2f;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #d32f2f;">${addressDist.withoutAddress.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Records without Address</div>
                    <div style="font-size: 0.8em; color: #d32f2f; margin-top: 5px;">${addressDist.withoutAddressPercentage}%</div>
                </div>
                <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                    <span class="risk-badge risk-${addressRiskLevel.toLowerCase()}" style="font-size: 1.2em;">${addressRiskLevel} RISK</span>
                    <div style="color: #666; font-size: 0.9em; margin-top: 5px;">Address Compliance Level</div>
                </div>
            </div>
        </div>

        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
            <h4 style="color: #388e3c; margin-bottom: 10px;">📋 Address Field Distribution</h4>
            <div style="display: grid; gap: 8px;">
                ${Object.entries(addressStats).map(([field, stats]) => {
                    const fieldName = field.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                    return `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: white; border-radius: 6px;">
                        <span><strong>${fieldName}</strong></span>
                        <span>${stats.populatedCount.toLocaleString()} (${stats.populatedPercentage}%)</span>
                    </div>
                    `;
                }).join('')}
            </div>

            <div style="margin-top: 15px; padding: 12px; background: white; border-radius: 6px; border-left: 3px solid #7b1fa2;">
                <p><strong>Most Common Address Type:</strong> ${mostCommonType[0].replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())} (${mostCommonType[1].populatedPercentage}%)</p>
                <p style="margin-top: 8px; font-size: 0.9em; color: #666;">
                    ${addressDist.withoutAddressPercentage <= 5 ? '✅ Excellent address completion rate - meets regulatory standards' :
                      addressDist.withoutAddressPercentage <= 10 ? '⚠️ Good address completion - minor gaps require attention' :
                      '❌ Poor address completion - immediate remediation required for CDD compliance'}
                </p>
            </div>
        </div>
    `;
    riskGrid.appendChild(addressCard);
}

// Export functions
function exportToCSV() {
    if (!currentData || currentData.length === 0) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    if (!analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('Data analysis not completed. Please analyze your data first.');
        return;
    }

    // Show CSV export options
    const recordCount = currentData.length;
    const completeRecords = currentData.filter((row, index) =>
        analysisResults.profileCompleteness[index] === 100
    ).length;
    const incompleteRecords = recordCount - completeRecords;

    const options = [
        'Analysis Report - Field analysis summary with risk levels',
        `Complete Records - Export ${completeRecords.toLocaleString()} fully compliant records (Streaming)`,
        `Incomplete Records - Export ${incompleteRecords.toLocaleString()} records requiring attention (Streaming)`,
        'Select Specific Fields - Choose individual fields to export (like NO_OF_PTO)',
        'Data Format Violations - Export records with improper formatting in critical fields',
        'Format Violations (Complete Records Only) - Analyze formatting issues in complete records',
        'Format Violations (Incomplete Records Only) - Analyze formatting issues in incomplete records',
        'Cancel'
    ];

    const choice = prompt(
        `CSV Export Options\n` +
        `Dataset: ${recordCount.toLocaleString()} total records\n` +
        `Complete: ${completeRecords.toLocaleString()} | Incomplete: ${incompleteRecords.toLocaleString()}\n\n` +
        `Choose export type:\n` +
        `1. ${options[0]}\n` +
        `2. ${options[1]}\n` +
        `3. ${options[2]}\n` +
        `4. ${options[3]}\n` +
        `5. ${options[4]}\n` +
        `6. ${options[5]}\n` +
        `7. ${options[6]}\n` +
        `8. ${options[7]}\n\n` +
        `Enter choice (1-8):`
    );

    switch (choice) {
        case '1':
            const csvContent = generateAnalysisCSV();
            downloadFile(csvContent, 'kyc-analysis-report.csv', 'text/csv');
            showSuccessMessage('✅ Analysis report exported successfully!');
            break;
        case '2':
            exportCompleteRecordsStreaming();
            break;
        case '3':
            exportIncompleteRecordsStreaming();
            break;
        case '4':
            showFieldSelectionDialogCSV();
            break;
        case '5':
            exportDataFormatViolations();
            break;
        case '6':
            exportFormatViolationsCompleteRecords();
            break;
        case '7':
            exportFormatViolationsIncompleteRecords();
            break;
        case '8':
        case null:
            showSuccessMessage('CSV export cancelled by user.');
            break;
        default:
            showErrorMessage('Invalid choice. Export cancelled.');
            break;
    }
}

function generateAnalysisCSV() {
    // Check if analysis results are available
    if (!analysisResults || !analysisResults.fieldAnalysis) {
        console.error('Analysis results not available. Please analyze data first.');
        return 'Error: No analysis data available. Please analyze your data first before exporting CSV.\n';
    }

    let csv = 'KYC Data Analysis Report - Enhanced Validation Applied\n';
    csv += 'Note: Address fields (ADD01, OFFICE_ADDRESS, HOME_ADDRESS, MAILING_ADDRESS) use group validation\n';
    csv += 'If ANY address field has data, ALL address fields are counted as complete for compliance\n';
    csv += 'Special Field Rules: NO_OF_PTO and NO_OF_SIGNATURE with value "0" are treated as BLANK/MISSING\n';
    csv += 'DEFAULT Field Rule: DEFAULT field is only BLANK if ALL other fields in the record are blank (reverse validation)\n\n';
    csv += 'Field Name,Total Records,Blank Count,Blank Percentage,Risk Level\n';

    Object.entries(analysisResults.fieldAnalysis)
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
        .forEach(([field, analysis]) => {
            csv += `"${field}",${analysis.totalRecords},${analysis.blankCount},${analysis.blankPercentage}%,${analysis.riskLevel}\n`;
        });

    // Add address analysis section
    if (analysisResults.addressAnalysis) {
        csv += '\n\nAddress Field Analysis\n';
        csv += 'Address Field,Populated Count,Populated Percentage\n';

        Object.entries(analysisResults.addressAnalysis.addressFieldStats)
            .forEach(([field, stats]) => {
                const fieldName = field.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                csv += `"${fieldName}",${stats.populatedCount},${stats.populatedPercentage}%\n`;
            });

        csv += '\nAddress Completion Summary\n';
        csv += 'Category,Count,Percentage\n';
        const dist = analysisResults.addressAnalysis.addressDistribution;
        csv += `"Records with Address",${dist.withAddress},${dist.withAddressPercentage}%\n`;
        csv += `"Records without Address",${dist.withoutAddress},${dist.withoutAddressPercentage}%\n`;
    }

    return csv;
}

function exportToExcel() {
    if (!currentData || currentData.length === 0) {
        showErrorMessage('No dataset is currently loaded. Please upload a CSV or Excel file first to export field analysis.');
        return;
    }

    if (!analysisResults || !analysisResults.availableFields) {
        showErrorMessage('Analysis results not available. Please analyze the data first before exporting.');
        return;
    }

    try {
        const recordCount = currentData.length;
        const fieldCount = analysisResults.availableFields.length;

        // Always offer export options including field selection
        const options = [
            'Summary Only (Analysis without individual records)',
            'Sample Data (First 25,000 records per field)',
            'Select Specific Fields (Choose which fields to export)',
            'Full Export (All fields and data)',
            'Cancel'
        ];

        const choice = prompt(
            `Dataset: ${recordCount.toLocaleString()} records, ${fieldCount} fields.\n\n` +
            `Choose export option:\n` +
            `1. ${options[0]}\n` +
            `2. ${options[1]}\n` +
            `3. ${options[2]} - Recommended\n` +
            `4. ${options[3]}${recordCount > 100000 ? ' (May be slow for large datasets)' : ''}\n` +
            `5. ${options[4]}\n\n` +
            `Enter choice (1-5):`
        );

        switch (choice) {
            case '1':
                exportSummaryOnlyExcel();
                return;
            case '2':
                exportSampleDataExcel(25000);
                return;
            case '3':
                showFieldSelectionDialog();
                return;
            case '4':
                // Continue with full export
                break;
            case '5':
            case null:
                showSuccessMessage('Excel export cancelled by user.');
                return;
            default:
                showErrorMessage('Invalid choice. Export cancelled.');
                return;
        }

        // Show progress indicator with dataset size info
        showSuccessMessage(`Generating Excel workbook: ${fieldCount} field sheets for ${recordCount.toLocaleString()} records...`);

        // Create workbook
        const workbook = XLSX.utils.book_new();
        const availableFields = analysisResults.availableFields;

        console.log(`Creating Excel workbook with ${availableFields.length} field sheets for ${currentData.length} records`);

        // Create a summary sheet first
        createSummarySheet(workbook);

        // Use optimized full export for all datasets
        exportOptimizedFullDataset(workbook, availableFields, currentData);

    } catch (error) {
        console.error('Excel export error:', error);
        showErrorMessage(`Failed to export Excel file: ${error.message}. Try using "Summary Only" option for large datasets.`);
    }
}

function exportStandardDatasetToExcel(workbook, availableFields, data) {
    // Create individual field sheets including ADDRESS_GROUP
    let processedFields = 0;
    const fieldsToExport = [...availableFields, 'ADDRESS_GROUP'];
    const totalFields = fieldsToExport.length;

    fieldsToExport.forEach((fieldName, index) => {
        try {
            createFieldSheet(workbook, fieldName, data);
            processedFields++;

            // Update progress for large datasets
            if (index % 5 === 0 || index === totalFields - 1) {
                const progress = Math.round((processedFields / totalFields) * 100);
                console.log(`Excel export progress: ${progress}% (${processedFields}/${totalFields} fields) - ${fieldName}`);
            }
        } catch (fieldError) {
            console.warn(`Error creating sheet for field ${fieldName}:`, fieldError);
            // Continue with other fields
        }
    });

    // Generate filename and save
    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `KYC_Field_Analysis_${currentDate}.xlsx`;

    console.log('Saving Excel workbook...');
    XLSX.writeFile(workbook, filename);

    showSuccessMessage(`Excel workbook exported successfully! File: ${filename}`);
    console.log(`Excel export complete: ${filename} with ${Object.keys(workbook.Sheets).length} sheets`);
}

function exportSummaryOnlyExcel() {
    try {
        showSuccessMessage('Generating summary-only Excel workbook...');

        const workbook = XLSX.utils.book_new();

        // Create enhanced summary sheet
        createSummarySheet(workbook);

        // Create field statistics sheet
        createFieldStatisticsSheet(workbook);

        // Create risk analysis sheet
        createRiskAnalysisSheet(workbook);

        const currentDate = new Date().toISOString().split('T')[0];
        const filename = `KYC_Summary_Analysis_${currentDate}.xlsx`;

        XLSX.writeFile(workbook, filename);
        showSuccessMessage(`Summary Excel workbook exported successfully! File: ${filename}`);

    } catch (error) {
        console.error('Summary export error:', error);
        showErrorMessage(`Failed to export summary Excel file: ${error.message}`);
    }
}

function exportSampleDataExcel(sampleSize) {
    try {
        showSuccessMessage(`Generating Excel workbook with sample data (${sampleSize.toLocaleString()} records per field)...`);

        const workbook = XLSX.utils.book_new();
        const availableFields = analysisResults.availableFields;

        // Create summary sheet
        createSummarySheet(workbook);

        // Create sample data sheets with CIF column including ADDRESS_GROUP
        let processedFields = 0;
        const fieldsToExport = [...availableFields, 'ADDRESS_GROUP'];
        fieldsToExport.forEach((fieldName, index) => {
            try {
                const sampleData = currentData.slice(0, sampleSize);
                createFieldSheet(workbook, fieldName, sampleData, true); // true indicates sample
                processedFields++;

                if (index % 5 === 0 || index === fieldsToExport.length - 1) {
                    const progress = Math.round((processedFields / fieldsToExport.length) * 100);
                    console.log(`Sample export progress: ${progress}% (${processedFields}/${fieldsToExport.length} fields) - ${fieldName}`);

                    if (index % 10 === 0 || index === fieldsToExport.length - 1) {
                        showSuccessMessage(`Sample export progress: ${progress}% (${processedFields}/${fieldsToExport.length} field sheets created)`);
                    }
                }
            } catch (fieldError) {
                console.warn(`Error creating sample sheet for field ${fieldName}:`, fieldError);
            }
        });

        const currentDate = new Date().toISOString().split('T')[0];
        const filename = `KYC_Sample_Analysis_${currentDate}.xlsx`;

        XLSX.writeFile(workbook, filename);
        showSuccessMessage(`✅ Sample Excel workbook exported successfully! File: ${filename} (BLANK records with 19 context columns from ${sampleSize.toLocaleString()} sample records per field)`);

    } catch (error) {
        console.error('Sample export error:', error);
        showErrorMessage(`Failed to export sample Excel file: ${error.message}`);
    }
}

function exportOptimizedFullDataset(workbook, availableFields, data) {
    try {
        const recordCount = data.length;
        showSuccessMessage(`Exporting full dataset: ${availableFields.length} field sheets for ${recordCount.toLocaleString()} records...`);

        let processedFields = 0;
        const totalFields = availableFields.length;

        // Process fields with optimized memory management
        for (let i = 0; i < availableFields.length; i++) {
            const fieldName = availableFields[i];

            try {
                // Create field sheet with full data but optimized processing
                createOptimizedFieldSheet(workbook, fieldName, data);
                processedFields++;

                // Update progress every 5 fields or at completion
                if (i % 5 === 0 || i === totalFields - 1) {
                    const progress = Math.round((processedFields / totalFields) * 100);
                    console.log(`Full export progress: ${progress}% (${processedFields}/${totalFields} fields) - ${fieldName}`);

                    if (i % 10 === 0 || i === totalFields - 1) {
                        showSuccessMessage(`Export progress: ${progress}% (${processedFields}/${totalFields} field sheets created)`);
                    }
                }

            } catch (fieldError) {
                console.warn(`Error creating sheet for field ${fieldName}:`, fieldError);
                // Continue with other fields
            }
        }

        const currentDate = new Date().toISOString().split('T')[0];
        const filename = `KYC_Field_Analysis_Full_${currentDate}.xlsx`;

        console.log('Saving full Excel workbook...');
        showSuccessMessage('Finalizing Excel workbook... Please wait.');

        // Use setTimeout to allow UI update before saving
        setTimeout(() => {
            try {
                XLSX.writeFile(workbook, filename);
                showSuccessMessage(`✅ Full Excel workbook exported successfully! File: ${filename} (${Object.keys(workbook.Sheets).length} sheets, BLANK records with 19 context columns for comprehensive remediation)`);
                console.log(`Full Excel export complete: ${filename} - BLANK records with context columns`);
            } catch (saveError) {
                console.error('Error saving full Excel file:', saveError);
                showErrorMessage(`Failed to save Excel file: ${saveError.message}. Try using "Sample Data" option for very large datasets.`);
            }
        }, 100);

    } catch (error) {
        console.error('Full export error:', error);
        showErrorMessage(`Failed to export full Excel file: ${error.message}`);
    }
}

function createSummarySheet(workbook) {
    const currentDate = new Date();
    const summaryData = [
        ['KYC Field Analysis - Comprehensive Report'],
        ['Generated on:', currentDate.toLocaleString()],
        ['Export Type:', 'Multi-Sheet Field Analysis'],
        [''],
        ['📊 Dataset Overview'],
        ['Total Records:', analysisResults.totalRecords.toLocaleString()],
        ['Complete Profiles:', analysisResults.completeProfiles.toLocaleString()],
        ['Incomplete Profiles:', analysisResults.incompleteProfiles.toLocaleString()],
        ['Overall Completion Rate:', `${analysisResults.completionRate}%`],
        ['Total Fields Analyzed:', analysisResults.availableFields.length],
        [''],
        ['🎯 Risk Assessment Summary'],
        ['Risk Level', 'Field Count', 'Avg Blank %']
    ];

    // Calculate risk level statistics
    const riskStats = {};
    Object.entries(analysisResults.fieldAnalysis).forEach(([field, analysis]) => {
        if (field !== 'ADDRESS_GROUP') {
            const risk = analysis.riskLevel;
            if (!riskStats[risk]) {
                riskStats[risk] = { count: 0, totalBlank: 0 };
            }
            riskStats[risk].count++;
            riskStats[risk].totalBlank += analysis.blankPercentage;
        }
    });

    Object.entries(riskStats).forEach(([risk, stats]) => {
        const avgBlank = (stats.totalBlank / stats.count).toFixed(1);
        summaryData.push([risk, stats.count, `${avgBlank}%`]);
    });

    summaryData.push(['']);
    summaryData.push(['📋 Detailed Field Analysis']);
    summaryData.push(['Field Name', 'Total Records', 'Filled Count', 'Blank Count', 'Completion %', 'Risk Level']);

    // Add field analysis data sorted by blank percentage (highest risk first)
    Object.entries(analysisResults.fieldAnalysis)
        .filter(([field]) => field !== 'ADDRESS_GROUP')
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
        .forEach(([field, analysis]) => {
            const filledCount = analysis.totalRecords - analysis.blankCount;
            const completionRate = (100 - analysis.blankPercentage).toFixed(1);

            summaryData.push([
                field,
                analysis.totalRecords.toLocaleString(),
                filledCount.toLocaleString(),
                analysis.blankCount.toLocaleString(),
                `${completionRate}%`,
                analysis.riskLevel
            ]);
        });

    // Add address analysis if available
    if (analysisResults.addressAnalysis) {
        summaryData.push(['']);
        summaryData.push(['🏠 Address Group Analysis']);
        summaryData.push(['Address Validation Rule:', 'Group validation - ANY address field satisfies requirement']);
        summaryData.push(['Records with Address:', analysisResults.addressAnalysis.addressDistribution.withAddress.toLocaleString()]);
        summaryData.push(['Records without Address:', analysisResults.addressAnalysis.addressDistribution.withoutAddress.toLocaleString()]);
        summaryData.push(['Address Compliance Rate:', `${analysisResults.addressAnalysis.addressDistribution.withAddressPercentage}%`]);

        summaryData.push(['']);
        summaryData.push(['Address Field Breakdown']);
        summaryData.push(['Address Field', 'Populated Count', 'Population %']);

        Object.entries(analysisResults.addressAnalysis.addressFieldStats).forEach(([field, stats]) => {
            summaryData.push([
                field,
                stats.populatedCount.toLocaleString(),
                `${stats.populatedPercentage}%`
            ]);
        });
    }

    // Add compliance validation rules
    summaryData.push(['']);
    summaryData.push(['⚖️ AML/KYC Compliance Rules Applied']);
    summaryData.push(['Rule Type', 'Description']);
    summaryData.push(['Address Group Validation', 'If ANY address field (ADD01, OFFICE_ADDRESS, HOME_ADDRESS, MAILING_ADDRESS) has data, ALL address fields are counted as complete for compliance']);
    summaryData.push(['Special Field Rules', 'NO_OF_PTO and NO_OF_SIGNATURE with value "0" are treated as BLANK/MISSING (zero signatures indicate incomplete documentation)']);
    summaryData.push(['DEFAULT Field Logic', 'DEFAULT field is only considered BLANK if ALL other fields in the record are blank (data quality indicator)']);
    summaryData.push(['Risk Level Assignment', 'CRITICAL: Core identity fields, HIGH: Financial/regulatory fields, MEDIUM: Contact/preference fields, LOW: Optional fields']);

    // Add export information
    summaryData.push(['']);
    summaryData.push(['📁 Export Information']);
    summaryData.push(['Workbook Structure:', 'Each field has its own dedicated worksheet']);
    summaryData.push(['Sheet Count:', `${analysisResults.availableFields.length + 1} sheets (${analysisResults.availableFields.length} field sheets + 1 summary)`]);
    summaryData.push(['Data Integrity:', 'All original field values preserved with completion status']);
    summaryData.push(['Excel Compatibility:', 'Optimized for Excel 2016+ with proper formatting and column widths']);

    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);

    // Set column widths for better readability
    summarySheet['!cols'] = [
        { width: 30 }, // Field names/descriptions
        { width: 18 }, // Numbers/counts
        { width: 15 }, // Numbers/percentages
        { width: 15 }, // Additional numbers
        { width: 12 }, // Percentages
        { width: 12 }  // Risk levels
    ];

    // Add title formatting (this will be visible in Excel)
    const titleRange = XLSX.utils.decode_range('A1:F1');
    if (!summarySheet['!merges']) summarySheet['!merges'] = [];
    summarySheet['!merges'].push(titleRange);

    XLSX.utils.book_append_sheet(workbook, summarySheet, '📊 Summary');
}

function createOptimizedFieldSheet(workbook, fieldName, data) {
    // Get field analysis for completion percentage
    const fieldAnalysis = analysisResults.fieldAnalysis[fieldName];
    const completionRate = fieldAnalysis ? (100 - fieldAnalysis.blankPercentage) : 0;
    const blankCount = fieldAnalysis ? fieldAnalysis.blankCount : 0;
    const filledCount = data.length - blankCount;

    // Define additional context columns in exact order
    const contextColumns = [
        'CONTACT_NO', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'NO_OF_PTO', 'OCCUPATION',
        'INDUSTRY_SECTOR', 'CUSTOMER_SEGMENT', 'CUSTOMER_SEGMENT_UDF', 'INCOME_LEVEL',
        'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'DEFAULT', 'OFFICE_ADDRESS',
        'EMPLOYMENTSTATUS', 'DATE_OF_BIRTH', 'NATIONALITY', 'FATHERNAME', 'NO_OF_SIGNATURE'
    ];

    // Create sheet data with enhanced header structure
    const sheetData = [];

    // Header and summary information
    sheetData.push([`${fieldName} - Remediation Focus (BLANK Records Only)`]);

    // Create header row with all columns
    const headerRow = ['Record #', 'CIF', 'Status', 'Priority', ...contextColumns, fieldName];
    sheetData.push(headerRow);

    // Summary rows with appropriate column count
    const totalCols = headerRow.length;
    const summaryRow1 = new Array(totalCols).fill('');
    summaryRow1[0] = 'TOTAL SUMMARY';
    summaryRow1[1] = `${data.length.toLocaleString()} total records`;
    summaryRow1[2] = `${completionRate.toFixed(1)}% complete`;
    summaryRow1[3] = `${filledCount.toLocaleString()} filled`;
    summaryRow1[4] = `Risk: ${fieldAnalysis ? fieldAnalysis.riskLevel : 'Unknown'}`;
    sheetData.push(summaryRow1);

    const summaryRow2 = new Array(totalCols).fill('');
    summaryRow2[0] = 'BLANK RECORDS';
    summaryRow2[1] = `${blankCount.toLocaleString()} need attention`;
    summaryRow2[2] = `${(100 - completionRate).toFixed(1)}% missing`;
    summaryRow2[3] = `Showing below ↓`;
    summaryRow2[4] = fieldAnalysis?.riskLevel === 'CRITICAL' ? 'URGENT' : 'HIGH';
    sheetData.push(summaryRow2);

    // Separator row
    sheetData.push(new Array(totalCols).fill(''));

    // Filter and process only BLANK records with context columns
    const batchSize = 10000;
    let blankRecordsProcessed = 0;

    console.log(`Processing field ${fieldName}: Starting batch processing of ${data.length} records`);

    for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);

        // Process batch and filter for BLANK records only
        const blankBatchRows = [];

        console.log(`Processing field ${fieldName}: Batch ${Math.floor(i/batchSize) + 1}, records ${i + 1} to ${Math.min(i + batchSize, data.length)}`);

        batch.forEach((row, batchIndex) => {
            const actualIndex = i + batchIndex;
            const fieldValue = row[fieldName];
            const cifValue = row['CIF'] || row['cif'] || ''; // Handle case variations

            // Special handling for ADDRESS_GROUP field
            let isEmpty;
            let displayValue;

            if (fieldName === 'ADDRESS_GROUP') {
                // ADDRESS_GROUP uses group validation logic
                const homeAddress = row['HOME_ADDRESS'] || '';
                const mailingAddress = row['MAILING_ADDRESS'] || '';
                const add01 = row['ADD01'] || '';
                const officeAddress = row['OFFICE_ADDRESS'] || '';

                // ADDRESS_GROUP is BLANK only when ALL address fields are empty
                isEmpty = !homeAddress && !mailingAddress && !add01 && !officeAddress;

                // Display value shows the group status
                displayValue = isEmpty ? '[ALL ADDRESSES BLANK]' : '[HAS ADDRESS DATA]';
            } else {
                // Standard field validation
                isEmpty = isFieldValueBlank(fieldName, fieldValue, row, analysisResults.availableFields);
                displayValue = fieldValue || '[BLANK]';
                if (typeof displayValue === 'string' && displayValue.length > 2000) {
                    displayValue = displayValue.substring(0, 1997) + '...';
                }
            }

            // Only include BLANK records
            if (isEmpty) {
                // displayValue already calculated above based on field type

                // Optimize CIF display
                let displayCIF = cifValue || '[NO CIF]';
                if (typeof displayCIF === 'string' && displayCIF.length > 50) {
                    displayCIF = displayCIF.substring(0, 47) + '...';
                }

                const priority = fieldAnalysis?.riskLevel === 'CRITICAL' ? 'URGENT' :
                               fieldAnalysis?.riskLevel === 'HIGH' ? 'HIGH' :
                               'MEDIUM';

                // Build row with all context columns
                const rowData = [
                    actualIndex + 1, // Record number (1-based)
                    displayCIF,      // CIF value
                    'BLANK',         // Status (always BLANK for filtered records)
                    priority         // Priority level
                ];

                // Add context column values
                contextColumns.forEach(colName => {
                    let contextValue = row[colName] || '';

                    // Handle special cases for context columns
                    if (!contextValue || contextValue === '') {
                        contextValue = '[BLANK]';
                    } else if (typeof contextValue === 'string' && contextValue.length > 100) {
                        // Truncate long context values for readability
                        contextValue = contextValue.substring(0, 97) + '...';
                    }

                    rowData.push(contextValue);
                });

                // Add the main field value at the end
                rowData.push(displayValue);

                blankBatchRows.push(rowData);
                blankRecordsProcessed++;
            }
        });

        // Add blank records to sheet data
        if (blankBatchRows.length > 0) {
            sheetData.push(...blankBatchRows);
            console.log(`Processing field ${fieldName}: Added ${blankBatchRows.length} BLANK records to sheet, total processed: ${blankRecordsProcessed}`);
        } else {
            console.log(`Processing field ${fieldName}: No BLANK records found in this batch`);
        }

        // Clear batch references
        batch.length = 0;
        blankBatchRows.length = 0;
    }

    console.log(`Processing field ${fieldName}: Completed batch processing, total BLANK records: ${blankRecordsProcessed}`);

    // Handle extremely large datasets - split into multiple sheets if needed
    const maxRowsPerSheet = 900000; // Leave room for headers and summaries
    let needsMultipleSheets = blankRecordsProcessed > maxRowsPerSheet;

    if (needsMultipleSheets) {
        console.log(`Processing field ${fieldName}: Large dataset detected (${blankRecordsProcessed} records). Attempting multiple sheets...`);

        try {
            // Create multiple sheets for large datasets
            const dataStartIndex = sheetData.findIndex(row => row[0] === 'Record #');
            console.log(`Found header at index: ${dataStartIndex}, total sheetData length: ${sheetData.length}`);

            if (dataStartIndex === -1) {
                console.warn('Header row not found! Cannot create multiple sheets. Falling back to single sheet.');
                needsMultipleSheets = false; // Override the flag to continue with single sheet
            } else {
                const headerAndSummary = sheetData.slice(0, dataStartIndex + 1); // Include header row
                const dataRows = sheetData.slice(dataStartIndex + 1);
                console.log(`Header and summary rows: ${headerAndSummary.length}, Data rows: ${dataRows.length}`);

                let sheetNumber = 1;
                for (let i = 0; i < dataRows.length; i += maxRowsPerSheet) {
                    const sheetRows = dataRows.slice(i, i + maxRowsPerSheet);
                    const currentSheetData = [...headerAndSummary, ...sheetRows];

                    const sheetName = dataRows.length > maxRowsPerSheet ?
                        `${fieldName}_Part${sheetNumber}` :
                        fieldName;

                    console.log(`Creating sheet: ${sheetName} with ${sheetRows.length} records (total sheet data: ${currentSheetData.length} rows)`);

                    // Create worksheet with optimized settings
                    const worksheet = XLSX.utils.aoa_to_sheet(currentSheetData, {
                        cellStyles: false,
                        cellFormula: false,
                        cellHTML: false
                    });

                    // Optimize worksheet settings for large data
                    if (!worksheet['!ref']) worksheet['!ref'] = 'A1';
                    worksheet['!cols'] = new Array(totalCols).fill({ wch: 15 });

                    // Add to workbook
                    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
                    console.log(`Successfully created and added sheet: ${sheetName}`);
                    sheetNumber++;
                }

                console.log(`Multi-sheet creation completed for ${fieldName}. Created ${sheetNumber - 1} sheets.`);
                return; // Exit early since we've handled the sheet creation
            }
        } catch (multiSheetError) {
            console.error(`Error in multi-sheet creation for ${fieldName}:`, multiSheetError);
            console.log('Falling back to single sheet creation...');
            needsMultipleSheets = false; // Fall back to single sheet
        }
    }

    // Continue with single sheet creation if not using multiple sheets

    // Add footer summary if no blank records found
    // totalCols already defined above as headerRow.length

    if (blankRecordsProcessed === 0) {
        const footerSeparator = new Array(totalCols).fill('');
        sheetData.push(footerSeparator);

        const noActionRow = new Array(totalCols).fill('');
        noActionRow[0] = '✅ NO ACTION NEEDED';
        noActionRow[1] = 'All records complete';
        noActionRow[2] = '100% filled';
        noActionRow[3] = 'COMPLETE';
        noActionRow[4] = 'EXCELLENT';
        sheetData.push(noActionRow);
    } else {
        // Add footer with remediation summary
        const footerSeparator = new Array(totalCols).fill('');
        sheetData.push(footerSeparator);

        const remediationRow = new Array(totalCols).fill('');
        remediationRow[0] = 'REMEDIATION NEEDED';
        remediationRow[1] = `${blankRecordsProcessed.toLocaleString()} records above`;
        remediationRow[2] = 'Require data entry';
        remediationRow[3] = 'ACTION REQUIRED';
        remediationRow[4] = fieldAnalysis?.riskLevel === 'CRITICAL' ? 'URGENT' : 'HIGH';
        sheetData.push(remediationRow);
    }

    // Create worksheet efficiently with memory optimization
    console.log(`Processing field ${fieldName}: Creating single worksheet with ${sheetData.length} total rows`);

    try {
        const worksheet = XLSX.utils.aoa_to_sheet(sheetData, {
            cellStyles: false,      // Disable styling for performance
            cellFormula: false,     // Disable formulas for performance
            cellHTML: false,        // Disable HTML for performance
            raw: true              // Use raw values for performance
        });

        console.log(`Processing field ${fieldName}: Worksheet created successfully`);

    // Set column widths for better readability with all context columns
    const columnWidths = [
        { width: 8 },   // Record number
        { width: 12 },  // CIF
        { width: 8 },   // Status
        { width: 10 },  // Priority
        { width: 15 },  // CONTACT_NO
        { width: 20 },  // FULL_NAME
        { width: 15 },  // NATIONAL_IDENTIFIER
        { width: 10 },  // NO_OF_PTO
        { width: 15 },  // OCCUPATION
        { width: 15 },  // INDUSTRY_SECTOR
        { width: 15 },  // CUSTOMER_SEGMENT
        { width: 15 },  // CUSTOMER_SEGMENT_UDF
        { width: 12 },  // INCOME_LEVEL
        { width: 25 },  // HOME_ADDRESS
        { width: 25 },  // MAILING_ADDRESS
        { width: 25 },  // ADD01
        { width: 10 },  // DEFAULT
        { width: 25 },  // OFFICE_ADDRESS
        { width: 15 },  // EMPLOYMENTSTATUS
        { width: 12 },  // DATE_OF_BIRTH
        { width: 12 },  // NATIONALITY
        { width: 15 },  // FATHERNAME
        { width: 12 },  // NO_OF_SIGNATURE
        { width: 30 }   // Field value (main field being analyzed)
    ];

    worksheet['!cols'] = columnWidths;

    // Clean field name for sheet name (Excel sheet names have restrictions)
    let cleanFieldName = fieldName.replace(/[\\\/\?\*\[\]:]/g, '_').substring(0, 31);

    // Ensure unique sheet name
    let finalSheetName = cleanFieldName;
    let counter = 1;
    while (workbook.Sheets[finalSheetName]) {
        finalSheetName = `${cleanFieldName.substring(0, 28)}_${counter}`;
        counter++;
    }

        console.log(`Processing field ${fieldName}: Adding worksheet to workbook with name: ${finalSheetName}`);
        XLSX.utils.book_append_sheet(workbook, worksheet, finalSheetName);
        console.log(`Processing field ${fieldName}: Worksheet successfully added to workbook`);

        // Comprehensive memory cleanup for large datasets
        sheetData.length = 0;
        if (typeof worksheet !== 'undefined') {
            worksheet = null;
        }

        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }

        console.log(`Processing field ${fieldName}: Worksheet created and memory cleaned up successfully`);

    } catch (worksheetError) {
        console.error(`Error creating worksheet for ${fieldName}:`, worksheetError);
        throw worksheetError;
    }

    // Log remediation summary
    console.log(`Field ${fieldName}: ${blankRecordsProcessed} blank records exported for remediation with ${contextColumns.length} context columns (${completionRate.toFixed(1)}% complete)`);
}

// Keep the original function for sample exports
function createFieldSheet(workbook, fieldName, data, isLimited = false) {
    // For sample exports, use the CIF-enabled optimized version but with limited data
    const maxRows = isLimited ? Math.min(25000, data.length) : data.length;
    const dataToProcess = data.slice(0, maxRows);

    if (data.length > maxRows) {
        console.warn(`Field ${fieldName}: Dataset limited to ${maxRows.toLocaleString()} rows for sample export`);
    }

    // Use the same BLANK-only filtering for sample exports
    createOptimizedFieldSheet(workbook, fieldName, dataToProcess);
}

function createFieldStatisticsSheet(workbook) {
    const statsData = [
        ['Field Statistics Summary'],
        ['Generated on:', new Date().toLocaleString()],
        [''],
        ['Field Name', 'Total Records', 'Filled Count', 'Blank Count', 'Completion %', 'Risk Level', 'Priority']
    ];

    // Add field statistics
    Object.entries(analysisResults.fieldAnalysis)
        .filter(([field]) => field !== 'ADDRESS_GROUP')
        .sort((a, b) => {
            // Sort by risk level first, then by blank percentage
            const riskOrder = { 'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3 };
            const aRisk = riskOrder[a[1].riskLevel] || 4;
            const bRisk = riskOrder[b[1].riskLevel] || 4;
            if (aRisk !== bRisk) return aRisk - bRisk;
            return b[1].blankPercentage - a[1].blankPercentage;
        })
        .forEach(([field, analysis]) => {
            const filledCount = analysis.totalRecords - analysis.blankCount;
            const completionRate = (100 - analysis.blankPercentage).toFixed(1);
            const priority = analysis.riskLevel === 'CRITICAL' ? 'HIGH' :
                           analysis.riskLevel === 'HIGH' ? 'MEDIUM' : 'LOW';

            statsData.push([
                field,
                analysis.totalRecords.toLocaleString(),
                filledCount.toLocaleString(),
                analysis.blankCount.toLocaleString(),
                `${completionRate}%`,
                analysis.riskLevel,
                priority
            ]);
        });

    const statsSheet = XLSX.utils.aoa_to_sheet(statsData);
    statsSheet['!cols'] = [
        { width: 25 }, { width: 15 }, { width: 15 }, { width: 15 },
        { width: 12 }, { width: 12 }, { width: 10 }
    ];

    XLSX.utils.book_append_sheet(workbook, statsSheet, 'Field Statistics');
}

function createRiskAnalysisSheet(workbook) {
    const riskData = [
        ['Risk Analysis Summary'],
        ['Generated on:', new Date().toLocaleString()],
        [''],
        ['Risk Assessment Overview'],
        ['Risk Level', 'Field Count', 'Avg Completion %', 'Fields']
    ];

    // Calculate risk statistics
    const riskStats = {};
    const riskFields = {};

    Object.entries(analysisResults.fieldAnalysis).forEach(([field, analysis]) => {
        if (field !== 'ADDRESS_GROUP') {
            const risk = analysis.riskLevel;
            if (!riskStats[risk]) {
                riskStats[risk] = { count: 0, totalCompletion: 0 };
                riskFields[risk] = [];
            }
            riskStats[risk].count++;
            riskStats[risk].totalCompletion += (100 - analysis.blankPercentage);
            riskFields[risk].push(field);
        }
    });

    // Add risk level summaries
    ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'].forEach(risk => {
        if (riskStats[risk]) {
            const avgCompletion = (riskStats[risk].totalCompletion / riskStats[risk].count).toFixed(1);
            const fieldList = riskFields[risk].slice(0, 5).join(', ') +
                            (riskFields[risk].length > 5 ? ` (+${riskFields[risk].length - 5} more)` : '');

            riskData.push([
                risk,
                riskStats[risk].count,
                `${avgCompletion}%`,
                fieldList
            ]);
        }
    });

    // Add detailed risk analysis
    riskData.push(['']);
    riskData.push(['Critical Fields Requiring Immediate Attention']);
    riskData.push(['Field Name', 'Completion %', 'Missing Records', 'Remediation Priority']);

    Object.entries(analysisResults.fieldAnalysis)
        .filter(([field, analysis]) => field !== 'ADDRESS_GROUP' && analysis.riskLevel === 'CRITICAL')
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
        .forEach(([field, analysis]) => {
            const completionRate = (100 - analysis.blankPercentage).toFixed(1);
            const priority = analysis.blankPercentage > 50 ? 'URGENT' :
                           analysis.blankPercentage > 25 ? 'HIGH' : 'MEDIUM';

            riskData.push([
                field,
                `${completionRate}%`,
                analysis.blankCount.toLocaleString(),
                priority
            ]);
        });

    const riskSheet = XLSX.utils.aoa_to_sheet(riskData);
    riskSheet['!cols'] = [
        { width: 25 }, { width: 15 }, { width: 15 }, { width: 40 }
    ];

    XLSX.utils.book_append_sheet(workbook, riskSheet, 'Risk Analysis');
}

function showFieldSelectionDialog() {
    try {
        const availableFields = analysisResults.availableFields;
        const fieldAnalysis = analysisResults.fieldAnalysis;

        // Create field selection interface
        createFieldSelectionModal(availableFields, fieldAnalysis);

    } catch (error) {
        console.error('Field selection dialog error:', error);
        showErrorMessage(`Failed to show field selection: ${error.message}`);
    }
}

function createFieldSelectionModal(availableFields, fieldAnalysis) {
    // Remove existing modal if present
    const existingModal = document.getElementById('fieldSelectionModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal HTML
    const modalHTML = `
        <div id="fieldSelectionModal" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; color: #2c3e50;">Select Fields for Excel Export</h3>
                    <button onclick="closeFieldSelectionModal()" style="
                        background: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 5px 10px;
                        cursor: pointer;
                    ">✕</button>
                </div>

                <div style="margin-bottom: 15px;">
                    <button onclick="selectAllFields()" style="
                        background: #3498db;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 15px;
                        margin-right: 10px;
                        cursor: pointer;
                    ">Select All</button>
                    <button onclick="selectNoneFields()" style="
                        background: #95a5a6;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 15px;
                        margin-right: 10px;
                        cursor: pointer;
                    ">Select None</button>
                    <button onclick="selectCriticalFields()" style="
                        background: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 15px;
                        margin-right: 10px;
                        cursor: pointer;
                    ">Critical Only</button>
                    <button onclick="selectHighRiskFields()" style="
                        background: #f39c12;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 15px;
                        cursor: pointer;
                    ">High Risk</button>
                </div>

                <div id="fieldCheckboxContainer" style="
                    max-height: 400px;
                    overflow-y: auto;
                    border: 1px solid #ddd;
                    padding: 15px;
                    border-radius: 4px;
                    background: #f8f9fa;
                ">
                    <!-- Field checkboxes will be inserted here -->
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button onclick="exportSelectedFields()" style="
                        background: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 12px 25px;
                        font-size: 16px;
                        cursor: pointer;
                        margin-right: 10px;
                    ">Export Selected Fields</button>
                    <button onclick="closeFieldSelectionModal()" style="
                        background: #95a5a6;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 12px 25px;
                        font-size: 16px;
                        cursor: pointer;
                    ">Cancel</button>
                </div>

                <div id="selectionSummary" style="
                    margin-top: 15px;
                    padding: 10px;
                    background: #e8f4f8;
                    border-radius: 4px;
                    font-size: 14px;
                    color: #2c3e50;
                ">
                    <strong>Selection Summary:</strong> <span id="selectedCount">0</span> of ${availableFields.length + 1} fields selected (including ADDRESS_GROUP)
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Populate field checkboxes
    populateFieldCheckboxes(availableFields, fieldAnalysis);

    // Update selection count
    updateSelectionCount();
}

function populateFieldCheckboxes(availableFields, fieldAnalysis) {
    const container = document.getElementById('fieldCheckboxContainer');

    // Include ADDRESS_GROUP in the field selection
    const allFields = [...availableFields, 'ADDRESS_GROUP'];

    // Sort fields by risk level and completion rate
    const sortedFields = allFields
        .sort((a, b) => {
            const aAnalysis = fieldAnalysis[a] || {};
            const bAnalysis = fieldAnalysis[b] || {};

            // Sort by risk level first
            const riskOrder = { 'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3 };
            const aRisk = riskOrder[aAnalysis.riskLevel] || 4;
            const bRisk = riskOrder[bAnalysis.riskLevel] || 4;

            if (aRisk !== bRisk) return aRisk - bRisk;

            // Then by completion rate (lower completion first)
            return (bAnalysis.blankPercentage || 0) - (aAnalysis.blankPercentage || 0);
        });

    let checkboxHTML = '';

    sortedFields.forEach(field => {
        const analysis = fieldAnalysis[field] || {};
        const completionRate = analysis.blankPercentage ? (100 - analysis.blankPercentage).toFixed(1) : '0.0';
        const riskLevel = analysis.riskLevel || 'UNKNOWN';
        const riskColor = {
            'CRITICAL': '#e74c3c',
            'HIGH': '#f39c12',
            'MEDIUM': '#f1c40f',
            'LOW': '#27ae60'
        }[riskLevel] || '#95a5a6';

        checkboxHTML += `
            <div style="
                display: flex;
                align-items: center;
                padding: 8px;
                margin-bottom: 5px;
                background: white;
                border-radius: 4px;
                border-left: 4px solid ${riskColor};
            ">
                <input type="checkbox"
                       id="field_${field}"
                       value="${field}"
                       onchange="updateSelectionCount()"
                       style="margin-right: 10px; transform: scale(1.2);">
                <label for="field_${field}" style="
                    flex: 1;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <span style="font-weight: 500;">${field}</span>
                    <span style="
                        font-size: 12px;
                        color: #666;
                        display: flex;
                        gap: 10px;
                    ">
                        <span style="color: ${riskColor}; font-weight: bold;">${riskLevel}</span>
                        <span>${completionRate}% complete</span>
                    </span>
                </label>
            </div>
        `;
    });

    container.innerHTML = checkboxHTML;
}

function closeFieldSelectionModal() {
    const modal = document.getElementById('fieldSelectionModal');
    if (modal) {
        modal.remove();
    }
}

function selectAllFields() {
    const checkboxes = document.querySelectorAll('#fieldCheckboxContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectionCount();
}

function selectNoneFields() {
    const checkboxes = document.querySelectorAll('#fieldCheckboxContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectionCount();
}

function selectCriticalFields() {
    const checkboxes = document.querySelectorAll('#fieldCheckboxContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        const fieldName = checkbox.value;
        const analysis = analysisResults.fieldAnalysis[fieldName];
        checkbox.checked = analysis && analysis.riskLevel === 'CRITICAL';
    });
    updateSelectionCount();
}

function selectHighRiskFields() {
    const checkboxes = document.querySelectorAll('#fieldCheckboxContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        const fieldName = checkbox.value;
        const analysis = analysisResults.fieldAnalysis[fieldName];
        checkbox.checked = analysis && (analysis.riskLevel === 'CRITICAL' || analysis.riskLevel === 'HIGH');
    });
    updateSelectionCount();
}

function updateSelectionCount() {
    const checkboxes = document.querySelectorAll('#fieldCheckboxContainer input[type="checkbox"]');
    const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    const totalCount = checkboxes.length;

    const countElement = document.getElementById('selectedCount');
    if (countElement) {
        countElement.textContent = selectedCount;
    }

    // Update summary text
    const summaryElement = document.getElementById('selectionSummary');
    if (summaryElement) {
        let summaryText = `<strong>Selection Summary:</strong> ${selectedCount} of ${totalCount} fields selected`;

        if (selectedCount > 0) {
            const selectedFields = Array.from(checkboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            // Calculate estimated export size
            const recordCount = currentData ? currentData.length : 0;
            const estimatedRows = selectedCount * (recordCount + 10); // +10 for headers and summary

            summaryText += `<br><small>Estimated export: ${estimatedRows.toLocaleString()} total rows across ${selectedCount} sheets</small>`;

            if (selectedCount <= 5) {
                summaryText += `<br><small>Selected: ${selectedFields.join(', ')}</small>`;
            }
        }

        summaryElement.innerHTML = summaryText;
    }
}

function exportSelectedFields() {
    const checkboxes = document.querySelectorAll('#fieldCheckboxContainer input[type="checkbox"]');
    const selectedFields = Array.from(checkboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

    if (selectedFields.length === 0) {
        showErrorMessage('Please select at least one field to export.');
        return;
    }

    // Close modal
    closeFieldSelectionModal();

    // Show confirmation
    const recordCount = currentData.length;
    const proceed = confirm(
        `Export ${selectedFields.length} selected fields?\n\n` +
        `Fields: ${selectedFields.slice(0, 5).join(', ')}${selectedFields.length > 5 ? ` (+${selectedFields.length - 5} more)` : ''}\n` +
        `Records: ${recordCount.toLocaleString()} per field\n` +
        `Total sheets: ${selectedFields.length + 1} (including summary)\n\n` +
        `Continue with export?`
    );

    if (!proceed) {
        showSuccessMessage('Export cancelled by user.');
        return;
    }

    // Perform export with selected fields
    exportCustomFieldSelection(selectedFields);
}

function exportCustomFieldSelection(selectedFields) {
    try {
        const recordCount = currentData.length;
        showSuccessMessage(`Exporting ${selectedFields.length} selected fields: ${recordCount.toLocaleString()} records per field...`);

        // Create workbook with memory optimization for large datasets
        const workbook = XLSX.utils.book_new();

        // Set workbook properties for large datasets
        workbook.Props = {
            Title: "KYC Field Analysis Export",
            Subject: "AML Compliance Data",
            Author: "KYC Remediation System",
            CreatedDate: new Date()
        };

        console.log(`Starting export of ${selectedFields.length} fields for ${currentData.length} records`);

        // Create summary sheet
        createSummarySheet(workbook);

        // Create selected field sheets with memory management
        let processedFields = 0;

        selectedFields.forEach((fieldName, index) => {
            console.log(`Processing field ${index + 1}/${selectedFields.length}: ${fieldName}`);

            // Force garbage collection between large field processing
            if (index > 0 && window.gc) {
                console.log(`Forcing garbage collection before processing ${fieldName}`);
                window.gc();
            }
            try {
                createOptimizedFieldSheet(workbook, fieldName, currentData);
                processedFields++;

                // Update progress
                if (index % 5 === 0 || index === selectedFields.length - 1) {
                    const progress = Math.round((processedFields / selectedFields.length) * 100);
                    const memoryInfo = performance.memory ?
                        ` (Memory: ${Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)}MB)` : '';
                    console.log(`Custom export progress: ${progress}% (${processedFields}/${selectedFields.length} fields) - ${fieldName}${memoryInfo}`);

                    if (index % 10 === 0 || index === selectedFields.length - 1) {
                        showSuccessMessage(`Export progress: ${progress}% (${processedFields}/${selectedFields.length} field sheets created)${memoryInfo}`);
                    }
                }

            } catch (fieldError) {
                console.warn(`Error creating sheet for selected field ${fieldName}:`, fieldError);
            }
        });

        // Save workbook
        const currentDate = new Date().toISOString().split('T')[0];
        const filename = `KYC_Selected_Fields_${currentDate}.xlsx`;

        console.log('Saving custom field selection Excel workbook...');
        showSuccessMessage('Finalizing Excel workbook... Please wait.');

        setTimeout(() => {
            try {
                console.log(`Writing Excel file with ${Object.keys(workbook.Sheets).length} sheets...`);

                // Use optimized write options for large files
                const writeOptions = {
                    bookType: 'xlsx',
                    type: 'binary',
                    compression: true,  // Enable compression for smaller file size
                    cellStyles: false   // Disable cell styles for performance
                };

                XLSX.writeFile(workbook, filename, writeOptions);

                // Final memory cleanup
                if (window.gc) {
                    console.log('Final garbage collection...');
                    window.gc();
                }
                showSuccessMessage(`✅ Custom Excel workbook exported successfully! File: ${filename} (${selectedFields.length} selected fields, BLANK records with 19 context columns for comprehensive remediation)`);
                console.log(`Custom field export complete: ${filename} - BLANK records with context columns`);
            } catch (saveError) {
                console.error('Error saving custom Excel file:', saveError);
                showErrorMessage(`Failed to save Excel file: ${saveError.message}`);
            }
        }, 100);

    } catch (error) {
        console.error('Custom field export error:', error);
        showErrorMessage(`Failed to export selected fields: ${error.message}`);
    }
}

function exportToPDF() {
    // Create a comprehensive report
    const reportContent = generateHTMLReport();

    // Open in new window for printing/PDF
    const printWindow = window.open('', '_blank', 'width=1200,height=800');
    printWindow.document.write(reportContent);
    printWindow.document.close();
    printWindow.focus();

    // Add instructions for landscape printing
    const instructionDiv = printWindow.document.createElement('div');
    instructionDiv.innerHTML = `
        <div style="position: fixed; top: 10px; right: 10px; background: #1976d2; color: white; padding: 15px; border-radius: 8px; z-index: 9999; font-family: Arial, sans-serif; font-size: 14px; box-shadow: 0 4px 12px rgba(0,0,0,0.3); max-width: 300px;">
            <h4 style="margin: 0 0 10px 0; font-size: 16px;">📄 PDF Export Instructions</h4>
            <p style="margin: 5px 0; font-size: 13px;">1. Press <strong>Ctrl+P</strong> to print</p>
            <p style="margin: 5px 0; font-size: 13px;">2. Choose <strong>"Save as PDF"</strong></p>
            <p style="margin: 5px 0; font-size: 13px;">3. Set orientation to <strong>"Landscape"</strong></p>
            <p style="margin: 5px 0; font-size: 13px;">4. Enable <strong>"Background graphics"</strong></p>
            <button onclick="this.parentElement.style.display='none'" style="background: white; color: #1976d2; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-top: 5px;">Got it!</button>
        </div>
    `;
    printWindow.document.body.appendChild(instructionDiv);

    // Trigger print dialog with a slight delay
    setTimeout(() => {
        printWindow.print();
    }, 1000);
}

function generateHTMLReport() {
    const currentDate = new Date().toLocaleDateString();
    const currentTime = new Date().toLocaleTimeString();

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <title>KYC Data Analysis Report</title>
        <style>
            /* Print-specific settings for color preservation and full width */
            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                @page {
                    margin: 0.3in 0.5in;
                    size: landscape;
                    orientation: landscape;
                }

                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    width: 100% !important;
                    max-width: none !important;
                }

                .container {
                    width: 100% !important;
                    max-width: none !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                table {
                    width: 100% !important;
                    font-size: 0.9em;
                }

                .risk-grid {
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
                    gap: 8px !important;
                }

                /* Optimize for landscape layout */
                .summary {
                    page-break-inside: avoid;
                }

                .risk-section {
                    page-break-inside: avoid;
                    break-inside: avoid;
                }

                /* Ensure tables fit landscape width */
                th, td {
                    padding: 8px 6px !important;
                    font-size: 0.85em !important;
                }

                h2 {
                    font-size: 1.5em !important;
                }

                h3 {
                    font-size: 1.2em !important;
                }
            }

            /* Base styles optimized for landscape */
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 8px;
                padding: 0;
                line-height: 1.5;
                color: #2c3e50;
                background: white;
                width: 100%;
                max-width: none;
                min-height: 100vh;
                box-sizing: border-box;
            }

            /* Full width container */
            .container {
                width: 100%;
                max-width: none;
                margin: 0;
                padding: 0;
            }

            /* Header styling */
            .header {
                text-align: center;
                margin: 0 0 30px 0;
                padding: 25px 20px;
                background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
                color: white;
                border-radius: 0;
                box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
                width: 100%;
                box-sizing: border-box;
            }

            .header h1 {
                margin: 0 0 10px 0;
                font-size: 2.5em;
                font-weight: 700;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            .header p {
                margin: 0;
                font-size: 1.1em;
                opacity: 0.9;
            }

            /* Executive Summary */
            .summary {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                padding: 20px;
                margin: 20px 0;
                border-radius: 8px;
                border-left: 6px solid #1976d2;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                width: 100%;
                box-sizing: border-box;
            }

            .summary h2 {
                color: #1976d2;
                margin-top: 0;
                font-size: 1.8em;
                border-bottom: 2px solid #1976d2;
                padding-bottom: 10px;
            }

            /* Table styling */
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                table-layout: fixed;
            }

            th {
                background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
                color: white;
                padding: 15px 12px;
                text-align: left;
                font-weight: 600;
                font-size: 0.95em;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            td {
                border: 1px solid #e0e0e0;
                padding: 12px;
                text-align: left;
                background: white;
            }

            /* Risk level colors */
            .risk-high {
                background-color: #ffebee !important;
                border-left: 4px solid #d32f2f !important;
            }

            .risk-medium {
                background-color: #fff3e0 !important;
                border-left: 4px solid #f57c00 !important;
            }

            .risk-low {
                background-color: #e8f5e8 !important;
                border-left: 4px solid #388e3c !important;
            }

            /* Risk badges */
            .risk-badge {
                padding: 6px 12px;
                border-radius: 20px;
                color: white !important;
                font-size: 0.85em;
                font-weight: bold;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                display: inline-block;
            }

            .risk-badge.critical { background: #d32f2f !important; }
            .risk-badge.high { background: #f57c00 !important; }
            .risk-badge.medium { background: #fbc02d !important; color: #333 !important; }
            .risk-badge.low { background: #388e3c !important; }
            .risk-badge.excellent { background: #388e3c !important; }
            .risk-badge.good { background: #1976d2 !important; }
            .risk-badge.moderate { background: #f57c00 !important; }
            .risk-badge.poor { background: #d32f2f !important; }

            /* Section headers */
            h2 {
                color: #1976d2;
                font-size: 1.8em;
                margin: 30px 0 20px 0;
                padding: 15px 0;
                border-bottom: 3px solid #1976d2;
                background: linear-gradient(90deg, rgba(25, 118, 210, 0.1) 0%, transparent 100%);
                padding-left: 15px;
                border-radius: 6px;
            }

            h3 {
                color: #1565c0;
                font-size: 1.4em;
                margin: 25px 0 15px 0;
                padding: 10px 0;
                border-bottom: 2px solid #e3f2fd;
            }

            h4 {
                color: #2e7d32;
                font-size: 1.2em;
                margin: 20px 0 10px 0;
            }

            /* Page breaks */
            .page-break {
                page-break-before: always;
                margin-top: 40px;
            }

            /* Strong text styling */
            strong {
                color: #1976d2;
                font-weight: 600;
            }

            /* List styling */
            ul {
                padding-left: 20px;
            }

            li {
                margin: 8px 0;
                line-height: 1.5;
            }

            /* Emphasis styling */
            em {
                color: #666;
                font-style: italic;
            }

            /* Risk Assessment Section Styles */
            .risk-section {
                margin: 20px 0;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                width: 100%;
                box-sizing: border-box;
            }

            .risk-overview {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-left: 6px solid #1976d2 !important;
            }

            .risk-priority {
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                border-left: 6px solid #1976d2 !important;
            }

            .risk-remediation {
                background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
                border-left: 6px solid #7b1fa2 !important;
            }

            .risk-regulatory {
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                border-left: 6px solid #1565c0 !important;
            }

            .risk-branch {
                background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
                border-left: 6px solid #6a1b9a !important;
            }

            .risk-monitoring {
                background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
                border-left: 6px solid #00695c !important;
            }

            /* Risk Grid Layout */
            .risk-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 12px;
                margin: 15px 0;
                width: 100%;
            }

            .risk-item {
                text-align: center;
                padding: 20px;
                background: white !important;
                border-radius: 8px;
                border-left: 4px solid #ccc !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            /* Icons and emojis */
            h3:before {
                margin-right: 8px;
            }

            /* Alert boxes */
            .alert-critical {
                background: #ffebee !important;
                border: 2px solid #d32f2f !important;
                border-radius: 8px;
                padding: 15px;
                margin: 15px 0;
            }

            .alert-warning {
                background: #fff3e0 !important;
                border: 2px solid #f57c00 !important;
                border-radius: 8px;
                padding: 15px;
                margin: 15px 0;
            }

            .alert-success {
                background: #e8f5e8 !important;
                border: 2px solid #388e3c !important;
                border-radius: 8px;
                padding: 15px;
                margin: 15px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
            <h1>🏦 KYC Data Analysis Report</h1>
            <p>📅 Generated on ${currentDate} at ${currentTime}</p>
            <p style="margin-top: 15px; font-size: 1.0em; opacity: 0.8;">AML Compliance & Risk Assessment Dashboard</p>
        </div>

        <div class="summary">
            <h2>📊 Executive Summary</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #1976d2; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div style="font-size: 2.2em; font-weight: bold; color: #1976d2;">${analysisResults.totalRecords.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Total Records Analyzed</div>
                </div>
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #388e3c; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div style="font-size: 2.2em; font-weight: bold; color: #388e3c;">${analysisResults.completeProfiles.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Complete Profiles</div>
                </div>
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #d32f2f; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div style="font-size: 2.2em; font-weight: bold; color: #d32f2f;">${analysisResults.incompleteProfiles.toLocaleString()}</div>
                    <div style="color: #666; font-size: 0.9em;">Incomplete Profiles</div>
                </div>
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid ${analysisResults.completionRate >= 90 ? '#388e3c' : analysisResults.completionRate >= 75 ? '#1976d2' : analysisResults.completionRate >= 60 ? '#f57c00' : '#d32f2f'}; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div style="font-size: 2.2em; font-weight: bold; color: ${analysisResults.completionRate >= 90 ? '#388e3c' : analysisResults.completionRate >= 75 ? '#1976d2' : analysisResults.completionRate >= 60 ? '#f57c00' : '#d32f2f'};">${analysisResults.completionRate}%</div>
                    <div style="color: #666; font-size: 0.9em;">Overall Completion Rate</div>
                </div>
            </div>

            <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #1976d2;">
                <h4 style="color: #1976d2; margin-top: 0;">📋 Analysis Method: Enhanced Validation Applied</h4>
                <ul style="margin: 10px 0; color: #555;">
                    <li><strong>Address Group Validation:</strong> If ANY address field (ADD01, OFFICE_ADDRESS, HOME_ADDRESS, MAILING_ADDRESS) contains data, ALL address fields are counted as complete for AML compliance purposes.</li>
                    <li><strong>Special Field Rules:</strong> NO_OF_PTO and NO_OF_SIGNATURE fields with value "0" are treated as BLANK/MISSING for compliance purposes.</li>
                    <li><strong>DEFAULT Field Rule:</strong> DEFAULT field is only considered BLANK if ALL other fields in the record are blank (reverse validation).</li>
                </ul>
            </div>
        </div>

        <h2>📋 Detailed Field Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>📝 Field Name</th>
                    <th>📊 Total Records</th>
                    <th>❌ Blank Count</th>
                    <th>📈 Blank Percentage</th>
                    <th>⚠️ Risk Level</th>
                </tr>
            </thead>
            <tbody>
                ${Object.entries(analysisResults.fieldAnalysis)
                    .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
                    .map(([field, analysis]) => `
                        <tr class="risk-${analysis.riskLevel.toLowerCase()}">
                            <td><strong>${field}</strong></td>
                            <td>${analysis.totalRecords.toLocaleString()}</td>
                            <td style="color: #d32f2f; font-weight: bold;">${analysis.blankCount.toLocaleString()}</td>
                            <td style="color: #d32f2f; font-weight: bold;">${analysis.blankPercentage}%</td>
                            <td><span class="risk-badge ${analysis.riskLevel.toLowerCase()}">${analysis.riskLevel}</span></td>
                        </tr>
                    `).join('')}
            </tbody>
        </table>

        ${analysisResults.addressAnalysis ? `
        <div class="page-break">
            <h2>Address Field Analysis</h2>

            <div class="summary">
                <h3>Address Completion Overview</h3>
                <p><strong>Records with Address:</strong> ${analysisResults.addressAnalysis.addressDistribution.withAddress.toLocaleString()} (${analysisResults.addressAnalysis.addressDistribution.withAddressPercentage}%)</p>
                <p><strong>Records without Address:</strong> ${analysisResults.addressAnalysis.addressDistribution.withoutAddress.toLocaleString()} (${analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage}%)</p>
            </div>

            <h3>Address Field Distribution</h3>
            <table>
                <thead>
                    <tr>
                        <th>Address Field Type</th>
                        <th>Populated Count</th>
                        <th>Populated Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.entries(analysisResults.addressAnalysis.addressFieldStats)
                        .sort((a, b) => b[1].populatedPercentage - a[1].populatedPercentage)
                        .map(([field, stats]) => {
                            const fieldName = field.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                            return `
                                <tr>
                                    <td>${fieldName}</td>
                                    <td>${stats.populatedCount.toLocaleString()}</td>
                                    <td>${stats.populatedPercentage}%</td>
                                </tr>
                            `;
                        }).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}

        <div class="page-break">
            <h2>AML Risk Assessment & Remediation Strategy</h2>

            ${generateHTMLRiskAssessment()}
        </div>
        </div> <!-- Close container -->
    </body>
    </html>
    `;
}

function generateHTMLRiskAssessment() {
    const highRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'HIGH');
    const mediumRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'MEDIUM');
    const lowRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'LOW');

    const overallRiskLevel = highRiskFields.length > 5 ? 'CRITICAL' :
                            highRiskFields.length > 2 ? 'HIGH' :
                            mediumRiskFields.length > 5 ? 'MEDIUM' : 'LOW';

    const complianceLevel = analysisResults.completionRate >= 90 ? 'EXCELLENT' :
                           analysisResults.completionRate >= 75 ? 'GOOD' :
                           analysisResults.completionRate >= 60 ? 'MODERATE' : 'POOR';

    const criticalFieldsAnalysis = criticalFields
        .filter(field => analysisResults.fieldAnalysis[field])
        .map(field => ({
            field,
            ...analysisResults.fieldAnalysis[field]
        }));

    // Calculate estimated workload
    const criticalWorkload = highRiskFields.reduce((sum, [, analysis]) => sum + analysis.blankCount, 0);
    const standardWorkload = mediumRiskFields.reduce((sum, [, analysis]) => sum + analysis.blankCount, 0);

    return `
        <style>
            .risk-section { margin: 20px 0; padding: 15px; border-radius: 8px; }
            .risk-overview { background: #f8f9fa; border-left: 4px solid #1976d2; }
            .risk-critical { background: #ffebee; border-left: 4px solid #d32f2f; }
            .risk-priority { background: #e3f2fd; border-left: 4px solid #1976d2; }
            .risk-remediation { background: #f3e5f5; border-left: 4px solid #7b1fa2; }
            .risk-regulatory { background: #e3f2fd; border-left: 4px solid #1565c0; }
            .risk-branch { background: #f3e5f5; border-left: 4px solid #6a1b9a; }
            .risk-monitoring { background: #e0f2f1; border-left: 4px solid #00695c; }
            .risk-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
            .risk-item { text-align: center; padding: 15px; background: white; border-radius: 6px; border-left: 3px solid #ccc; }
            .risk-badge { padding: 4px 12px; border-radius: 12px; color: white; font-size: 0.9em; font-weight: bold; }
            .risk-badge.critical { background: #d32f2f; }
            .risk-badge.high { background: #f57c00; }
            .risk-badge.medium { background: #fbc02d; }
            .risk-badge.low { background: #388e3c; }
            .risk-badge.excellent { background: #388e3c; }
            .risk-badge.good { background: #1976d2; }
            .risk-badge.moderate { background: #f57c00; }
            .risk-badge.poor { background: #d32f2f; }
        </style>

        <div class="risk-section risk-overview">
            <h3>🎯 Risk Assessment Overview</h3>
            <p><strong>Overall Risk Level:</strong> <span class="risk-badge ${overallRiskLevel.toLowerCase()}">${overallRiskLevel}</span></p>

            <div class="risk-grid">
                <div class="risk-item" style="border-left-color: #c62828;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #c62828;">${highRiskFields.length}</div>
                    <div>Critical Fields</div>
                </div>
                <div class="risk-item" style="border-left-color: #f57c00;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #f57c00;">${mediumRiskFields.length}</div>
                    <div>Medium Risk Fields</div>
                </div>
                <div class="risk-item" style="border-left-color: #388e3c;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #388e3c;">${lowRiskFields.length}</div>
                    <div>Low Risk Fields</div>
                </div>
            </div>

            ${highRiskFields.length > 0 ? `
            <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <h4 style="color: #c62828;">🚨 Critical Compliance Gaps</h4>
                <p><strong>Immediate Action Required:</strong> ${highRiskFields.length} fields pose significant AML compliance risks</p>
                <ul>
                    ${highRiskFields.slice(0, 5).map(([field, analysis]) =>
                        `<li><strong>${field}</strong>: ${analysis.blankPercentage}% missing (${analysis.blankCount.toLocaleString()} records)</li>`
                    ).join('')}
                    ${highRiskFields.length > 5 ? `<li><em>... and ${highRiskFields.length - 5} more critical fields</em></li>` : ''}
                </ul>
            </div>
            ` : ''}
        </div>

        <div class="risk-section risk-priority">
            <h3>📋 AML Compliance Priority Matrix</h3>

            <h4>🔍 Critical AML Fields Assessment</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Missing %</th>
                        <th>Missing Count</th>
                        <th>Risk Level</th>
                        <th>Urgency</th>
                    </tr>
                </thead>
                <tbody>
                    ${criticalFieldsAnalysis.map(analysis => {
                        const urgency = analysis.blankPercentage > 50 ? 'IMMEDIATE' :
                                       analysis.blankPercentage > 25 ? '30 DAYS' : '90 DAYS';
                        return `
                        <tr class="risk-${analysis.riskLevel.toLowerCase()}">
                            <td><strong>${analysis.field}</strong></td>
                            <td>${analysis.blankPercentage}%</td>
                            <td>${analysis.blankCount.toLocaleString()}</td>
                            <td><span class="risk-badge ${analysis.riskLevel.toLowerCase()}">${analysis.riskLevel}</span></td>
                            <td><strong>${urgency}</strong></td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div class="risk-grid">
                <div class="risk-item" style="border-left-color: #d32f2f;">
                    <h4 style="color: #d32f2f;">🚨 IMMEDIATE (0-7 Days)</h4>
                    <p><strong>Fields with >50% missing data</strong></p>
                    <p style="font-size: 0.9em;">Critical regulatory compliance risk. Requires immediate senior management attention.</p>
                </div>
                <div class="risk-item" style="border-left-color: #f57c00;">
                    <h4 style="color: #f57c00;">⚠️ HIGH PRIORITY (30 Days)</h4>
                    <p><strong>Fields with 25-50% missing data</strong></p>
                    <p style="font-size: 0.9em;">Significant compliance gaps. Requires structured remediation plan.</p>
                </div>
                <div class="risk-item" style="border-left-color: #388e3c;">
                    <h4 style="color: #388e3c;">✅ STANDARD (90 Days)</h4>
                    <p><strong>Fields with <25% missing data</strong></p>
                    <p style="font-size: 0.9em;">Manageable gaps. Regular operational processes with monthly monitoring.</p>
                </div>
            </div>
        </div>

        <div class="risk-section risk-remediation">
            <h3>🎯 KYC Remediation Action Plan</h3>

            <h4>📋 Immediate Action Items</h4>
            <ul>
                <li><strong>Escalate to Senior Management:</strong> ${highRiskFields.length} critical fields require executive attention</li>
                <li><strong>Activate Emergency Protocols:</strong> Deploy additional resources for high-risk field completion</li>
                <li><strong>Regulatory Notification:</strong> Prepare compliance status report for regulatory authorities</li>
                <li><strong>Customer Outreach:</strong> Initiate immediate contact for incomplete high-risk profiles</li>
                <li><strong>System Flags:</strong> Mark incomplete profiles for enhanced monitoring and transaction restrictions</li>
            </ul>

            <h4>🔄 Systematic Remediation Approach</h4>
            <div class="risk-grid">
                <div class="risk-item">
                    <h5 style="color: #2e7d32;">Phase 1: Critical Fields (0-30 days)</h5>
                    <ul style="text-align: left; font-size: 0.9em;">
                        <li>NATIONAL_IDENTIFIER, DATE_OF_BIRTH</li>
                        <li>FULL_NAME, CONTACT_NO</li>
                        <li>Address verification (any address type)</li>
                        <li>Occupation and income source</li>
                    </ul>
                </div>
                <div class="risk-item">
                    <h5 style="color: #2e7d32;">Phase 2: Supporting Fields (30-60 days)</h5>
                    <ul style="text-align: left; font-size: 0.9em;">
                        <li>NO_OF_PTO, NO_OF_SIGNATURE validation</li>
                        <li>Secondary contact information</li>
                        <li>Additional address types</li>
                        <li>Enhanced due diligence fields</li>
                    </ul>
                </div>
            </div>

            <h4>⚖️ Risk Categorization & Regulatory Impact</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Field Category</th>
                        <th>Regulatory Impact</th>
                        <th>Compliance Risk</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong style="color: #d32f2f;">Critical AML Fields</strong><br><small>NATIONAL_IDENTIFIER, FULL_NAME, DATE_OF_BIRTH, ADDRESS</small></td>
                        <td>Required for CDD compliance</td>
                        <td style="color: #d32f2f;"><strong>HIGH - Regulatory sanctions risk</strong></td>
                    </tr>
                    <tr>
                        <td><strong style="color: #f57c00;">Enhanced Due Diligence</strong><br><small>OCCUPATION, CONTACT_NO, NO_OF_PTO</small></td>
                        <td>Required for risk assessment</td>
                        <td style="color: #f57c00;"><strong>MEDIUM - Transaction monitoring impact</strong></td>
                    </tr>
                    <tr>
                        <td><strong style="color: #388e3c;">Supporting Documentation</strong><br><small>NO_OF_SIGNATURE, DEFAULT</small></td>
                        <td>Quality indicators</td>
                        <td style="color: #388e3c;"><strong>LOW - Audit findings only</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="risk-section risk-regulatory">
            <h3>⚖️ Regulatory Compliance Impact Assessment</h3>

            <h4>📊 Overall Compliance Status</h4>
            <div class="risk-grid">
                <div class="risk-item" style="border-left-color: ${complianceLevel === 'EXCELLENT' ? '#388e3c' : complianceLevel === 'GOOD' ? '#1976d2' : complianceLevel === 'MODERATE' ? '#f57c00' : '#d32f2f'};">
                    <div style="font-size: 2.2em; font-weight: bold; color: ${complianceLevel === 'EXCELLENT' ? '#388e3c' : complianceLevel === 'GOOD' ? '#1976d2' : complianceLevel === 'MODERATE' ? '#f57c00' : '#d32f2f'};">${analysisResults.completionRate}%</div>
                    <div>Profile Completion Rate</div>
                    <span class="risk-badge ${complianceLevel.toLowerCase()}">${complianceLevel}</span>
                </div>
                <div class="risk-item" style="border-left-color: #388e3c;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #388e3c;">${analysisResults.completeProfiles.toLocaleString()}</div>
                    <div>Compliant Profiles</div>
                </div>
                <div class="risk-item" style="border-left-color: #d32f2f;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #d32f2f;">${analysisResults.incompleteProfiles.toLocaleString()}</div>
                    <div>Non-Compliant Profiles</div>
                </div>
            </div>

            <h4>🏛️ Regulatory Exposure Analysis</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Compliance Area</th>
                        <th>Risk Level</th>
                        <th>Impact Assessment</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="${complianceLevel === 'POOR' ? 'risk-high' : 'risk-low'}">
                        <td><strong>BSA/AML Compliance Risk</strong></td>
                        <td><span class="risk-badge ${complianceLevel === 'POOR' ? 'high' : complianceLevel === 'MODERATE' ? 'medium' : 'low'}">${complianceLevel === 'POOR' ? 'HIGH' : complianceLevel === 'MODERATE' ? 'MEDIUM' : 'LOW'}</span></td>
                        <td>${complianceLevel === 'POOR' ? 'Significant regulatory sanctions risk' : complianceLevel === 'MODERATE' ? 'Potential examination findings' : 'Meets regulatory expectations'}</td>
                    </tr>
                    <tr>
                        <td><strong>CDD Requirements</strong></td>
                        <td><span class="risk-badge ${analysisResults.completionRate < 75 ? 'high' : 'low'}">${analysisResults.completionRate < 75 ? 'NON-COMPLIANT' : 'COMPLIANT'}</span></td>
                        <td>${analysisResults.completionRate < 75 ? 'Non-compliant with CDD rule requirements' : 'Generally compliant with enhanced monitoring needed'}</td>
                    </tr>
                    <tr>
                        <td><strong>Examination Readiness</strong></td>
                        <td><span class="risk-badge ${complianceLevel === 'EXCELLENT' ? 'excellent' : complianceLevel === 'GOOD' ? 'good' : complianceLevel === 'MODERATE' ? 'moderate' : 'poor'}">${complianceLevel}</span></td>
                        <td>${complianceLevel === 'EXCELLENT' ? 'Examination ready' : complianceLevel === 'GOOD' ? 'Minor remediation needed' : 'Significant preparation required'}</td>
                    </tr>
                </tbody>
            </table>

            <h4>📋 Compliance Recommendations</h4>
            <ul>
                ${complianceLevel === 'POOR' ? `
                <li><strong>Immediate:</strong> Implement emergency remediation protocols and notify board of directors</li>
                <li><strong>Regulatory:</strong> Consider voluntary disclosure to regulatory authorities</li>
                <li><strong>Operations:</strong> Halt new account openings until compliance gaps are addressed</li>
                ` : complianceLevel === 'MODERATE' ? `
                <li><strong>Priority:</strong> Accelerate remediation efforts with additional resources</li>
                <li><strong>Monitoring:</strong> Implement enhanced transaction monitoring for incomplete profiles</li>
                <li><strong>Reporting:</strong> Increase compliance reporting frequency to senior management</li>
                ` : `
                <li><strong>Maintenance:</strong> Continue regular monitoring and quality assurance processes</li>
                <li><strong>Enhancement:</strong> Focus on process improvements and automation opportunities</li>
                <li><strong>Training:</strong> Maintain staff competency through ongoing AML training programs</li>
                `}
            </ul>
        </div>

        <div class="risk-section risk-branch">
            <h3>🏢 Branch Assignment & Resource Allocation Strategy</h3>

            <h4>📊 Workload Distribution Analysis</h4>
            <div class="risk-grid">
                <div class="risk-item" style="border-left-color: #d32f2f;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #d32f2f;">${criticalWorkload.toLocaleString()}</div>
                    <div>Critical Remediation Items</div>
                    <div style="font-size: 0.8em; color: #d32f2f; margin-top: 5px;">Immediate Priority</div>
                </div>
                <div class="risk-item" style="border-left-color: #f57c00;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #f57c00;">${standardWorkload.toLocaleString()}</div>
                    <div>Standard Remediation Items</div>
                    <div style="font-size: 0.8em; color: #f57c00; margin-top: 5px;">30-90 Day Timeline</div>
                </div>
                <div class="risk-item" style="border-left-color: #6a1b9a;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #6a1b9a;">${analysisResults.incompleteProfiles.toLocaleString()}</div>
                    <div>Total Profiles to Review</div>
                    <div style="font-size: 0.8em; color: #6a1b9a; margin-top: 5px;">All Branches</div>
                </div>
            </div>

            <h4>🎯 Branch Assignment Methodology</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Branch Tier</th>
                        <th>Specialization</th>
                        <th>Capacity</th>
                        <th>Assignment Criteria</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Tier 1 Branches</strong><br><small>High-Risk Specialists</small></td>
                        <td>Complex cases requiring enhanced due diligence</td>
                        <td>50-75 profiles per week</td>
                        <td>Profiles with >3 critical missing fields</td>
                    </tr>
                    <tr>
                        <td><strong>Tier 2 Branches</strong><br><small>Standard Processing</small></td>
                        <td>Standard KYC documentation gaps</td>
                        <td>100-150 profiles per week</td>
                        <td>Profiles with 1-2 missing critical fields</td>
                    </tr>
                </tbody>
            </table>

            <h4>⏱️ Completion Timeline & Resource Requirements</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Priority Level</th>
                        <th>Timeline</th>
                        <th>FTE Required</th>
                        <th>Staff Level</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="risk-high">
                        <td><strong>IMMEDIATE</strong><br><small>Critical compliance gaps</small></td>
                        <td>0-7 Days</td>
                        <td>2-3 FTE</td>
                        <td>Senior compliance officers</td>
                    </tr>
                    <tr class="risk-medium">
                        <td><strong>HIGH PRIORITY</strong><br><small>Structured remediation</small></td>
                        <td>30 Days</td>
                        <td>4-6 FTE</td>
                        <td>Mixed experience levels</td>
                    </tr>
                    <tr class="risk-low">
                        <td><strong>STANDARD</strong><br><small>Regular operational processes</small></td>
                        <td>90 Days</td>
                        <td>6-8 FTE</td>
                        <td>Standard branch staff</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="risk-section risk-monitoring">
            <h3>📈 Monitoring Framework & Escalation Protocols</h3>

            <h4>📊 Progress Tracking & KPIs</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Frequency</th>
                        <th>Metrics</th>
                        <th>Responsibility</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Daily</strong></td>
                        <td>Profiles completed per branch, Critical field completion rate, Customer contact success rate</td>
                        <td>Branch Managers</td>
                    </tr>
                    <tr>
                        <td><strong>Weekly</strong></td>
                        <td>Branch performance comparison, Resource allocation effectiveness, Escalation case analysis</td>
                        <td>Regional Compliance</td>
                    </tr>
                    <tr>
                        <td><strong>Monthly</strong></td>
                        <td>Overall completion percentage, Regulatory compliance status, Risk level trend analysis</td>
                        <td>Senior Management</td>
                    </tr>
                </tbody>
            </table>

            <h4>🚨 Escalation Triggers & Response Protocols</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Escalation Level</th>
                        <th>Triggers</th>
                        <th>Response Time</th>
                        <th>Action Required</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="risk-high">
                        <td><strong>LEVEL 1: Branch Manager</strong></td>
                        <td>Daily target missed by >20%, Critical field completion rate <50%</td>
                        <td>IMMEDIATE</td>
                        <td>Resource reallocation, Staff coaching</td>
                    </tr>
                    <tr class="risk-medium">
                        <td><strong>LEVEL 2: Regional Compliance</strong></td>
                        <td>Weekly targets missed by >15%, Multiple branches underperforming</td>
                        <td>24 HOURS</td>
                        <td>Process review, Additional resources</td>
                    </tr>
                    <tr class="risk-high">
                        <td><strong>LEVEL 3: Senior Management</strong></td>
                        <td>Monthly completion rate <75%, Regulatory deadline at risk</td>
                        <td>48 HOURS</td>
                        <td>Executive intervention, Emergency protocols</td>
                    </tr>
                </tbody>
            </table>

            <h4>✅ Success Metrics & Quality Assurance</h4>
            <div class="risk-grid">
                <div class="risk-item">
                    <h5>Completion Quality Standards</h5>
                    <ul style="text-align: left; font-size: 0.9em;">
                        <li><strong>95%</strong> accuracy rate for critical fields</li>
                        <li><strong>100%</strong> documentation verification</li>
                        <li><strong>90%</strong> customer contact success rate</li>
                        <li><strong>Zero</strong> regulatory compliance gaps</li>
                    </ul>
                </div>
                <div class="risk-item">
                    <h5>Performance Benchmarks</h5>
                    <ul style="text-align: left; font-size: 0.9em;">
                        <li><strong>Target:</strong> 15-20 profiles per FTE per day</li>
                        <li><strong>Quality:</strong> <5% rework rate</li>
                        <li><strong>Timeline:</strong> 100% adherence to priority schedules</li>
                        <li><strong>Customer:</strong> <24hr response time</li>
                    </ul>
                </div>
            </div>
        </div>

        ${analysisResults.addressAnalysis ? `
        <div class="risk-section" style="background: #f3e5f5; border-left: 4px solid #7b1fa2;">
            <h3>🏠 Address Verification Analysis</h3>

            <h4>📍 Address Completion Overview</h4>
            <div class="risk-grid">
                <div class="risk-item" style="border-left-color: #388e3c;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #388e3c;">${analysisResults.addressAnalysis.addressDistribution.withAddress.toLocaleString()}</div>
                    <div>Records with Address</div>
                    <div style="font-size: 0.8em; color: #388e3c; margin-top: 5px;">${analysisResults.addressAnalysis.addressDistribution.withAddressPercentage}%</div>
                </div>
                <div class="risk-item" style="border-left-color: #d32f2f;">
                    <div style="font-size: 1.8em; font-weight: bold; color: #d32f2f;">${analysisResults.addressAnalysis.addressDistribution.withoutAddress.toLocaleString()}</div>
                    <div>Records without Address</div>
                    <div style="font-size: 0.8em; color: #d32f2f; margin-top: 5px;">${analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage}%</div>
                </div>
                <div class="risk-item">
                    <span class="risk-badge ${analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage > 10 ? 'high' : analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage > 5 ? 'medium' : 'low'}">${analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage > 10 ? 'HIGH' : analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage > 5 ? 'MEDIUM' : 'LOW'} RISK</span>
                    <div style="margin-top: 5px;">Address Compliance Level</div>
                </div>
            </div>

            <h4>📋 Address Field Distribution</h4>
            <table style="width: 100%; margin: 15px 0;">
                <thead>
                    <tr>
                        <th>Address Field Type</th>
                        <th>Populated Count</th>
                        <th>Populated Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.entries(analysisResults.addressAnalysis.addressFieldStats)
                        .sort((a, b) => b[1].populatedPercentage - a[1].populatedPercentage)
                        .map(([field, stats]) => {
                            const fieldName = field.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                            return `
                                <tr>
                                    <td><strong>${fieldName}</strong></td>
                                    <td>${stats.populatedCount.toLocaleString()}</td>
                                    <td>${stats.populatedPercentage}%</td>
                                </tr>
                            `;
                        }).join('')}
                </tbody>
            </table>

            <div style="background: white; padding: 15px; border-radius: 6px; margin-top: 15px;">
                <p><strong>Assessment:</strong> ${analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage <= 5 ? '✅ Excellent address completion rate - meets regulatory standards' : analysisResults.addressAnalysis.addressDistribution.withoutAddressPercentage <= 10 ? '⚠️ Good address completion - minor gaps require attention' : '❌ Poor address completion - immediate remediation required for CDD compliance'}</p>
            </div>
        </div>
        ` : ''}
    `;
}

function exportIncompleteRecords() {
    if (!currentData || currentData.length === 0 || !analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    const incompleteRecords = currentData.filter((row, index) =>
        analysisResults.profileCompleteness[index] < 100
    );

    if (incompleteRecords.length === 0) {
        showSuccessMessage('🎉 Excellent! No incomplete records found. All customer profiles are complete.');
        return;
    }

    let csv = 'Incomplete KYC Records - Requiring Attention\n';
    csv += `Total Incomplete Records: ${incompleteRecords.length} out of ${currentData.length}\n`;
    csv += `Completion Rate: ${(((currentData.length - incompleteRecords.length) / currentData.length) * 100).toFixed(1)}%\n`;
    csv += 'Note: Records with less than 100% profile completeness\n';
    csv += 'Address Group Validation: If ANY address field has data, ALL address fields count as complete\n';
    csv += 'Special Field Rules: NO_OF_PTO and NO_OF_SIGNATURE with value "0" are treated as BLANK/MISSING\n';
    csv += 'DEFAULT Field Rule: DEFAULT field is only BLANK if ALL other fields in the record are blank\n\n';

    const headers = analysisResults.availableFields;
    csv += headers.join(',') + ',Profile_Completeness_Percentage\n';

    incompleteRecords.forEach((row, originalIndex) => {
        const values = headers.map(header => {
            const value = row[header] || '';
            return `"${value.toString().replace(/"/g, '""')}"`;
        });

        // Find the original index to get the correct completeness percentage
        const dataIndex = currentData.findIndex(r => r === row);
        const completeness = analysisResults.profileCompleteness[dataIndex] || 0;
        values.push(`${completeness.toFixed(1)}%`);

        csv += values.join(',') + '\n';
    });

    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `KYC_Incomplete_Records_${currentDate}.csv`;
    downloadFile(csv, filename, 'text/csv');

    showSuccessMessage(`✅ Incomplete records exported! File: ${filename} (${incompleteRecords.length} records requiring attention)`);
}

function exportCompleteRecords() {
    if (!currentData || currentData.length === 0 || !analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    showSuccessMessage('Generating complete KYC records CSV...');

    const completeRecords = currentData.filter((row, index) =>
        analysisResults.profileCompleteness[index] === 100
    );

    if (completeRecords.length === 0) {
        showErrorMessage('No complete records found. All customer profiles require additional information.');
        return;
    }

    let csv = 'Complete KYC Records - Fully Compliant\n';
    csv += `Total Complete Records: ${completeRecords.length} out of ${currentData.length}\n`;
    csv += `Completion Rate: ${((completeRecords.length / currentData.length) * 100).toFixed(1)}%\n`;
    csv += 'Note: Records with 100% profile completeness meeting all AML/KYC requirements\n';
    csv += 'Address Group Validation: If ANY address field has data, ALL address fields count as complete\n';
    csv += 'Special Field Rules: NO_OF_PTO and NO_OF_SIGNATURE with value "0" are treated as BLANK/MISSING\n';
    csv += 'DEFAULT Field Rule: DEFAULT field is only BLANK if ALL other fields in the record are blank\n\n';

    const headers = analysisResults.availableFields;
    csv += headers.join(',') + ',Profile_Completeness_Percentage\n';

    completeRecords.forEach((row, originalIndex) => {
        const values = headers.map(header => {
            const value = row[header] || '';
            return `"${value.toString().replace(/"/g, '""')}"`;
        });

        // Find the original index to get the correct completeness percentage
        const dataIndex = currentData.findIndex(r => r === row);
        const completeness = analysisResults.profileCompleteness[dataIndex] || 0;
        values.push(`${completeness.toFixed(1)}%`);

        csv += values.join(',') + '\n';
    });

    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `KYC_Complete_Records_${currentDate}.csv`;
    downloadFile(csv, filename, 'text/csv');

    showSuccessMessage(`✅ Complete KYC records exported! File: ${filename} (${completeRecords.length} fully compliant records)`);
}

// Streaming CSV export functions for better performance
function exportCompleteRecordsStreaming() {
    if (!currentData || currentData.length === 0 || !analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    showSuccessMessage('🚀 Starting streaming export of complete KYC records...');

    // Use streaming approach with batches
    const batchSize = 5000;
    const headers = analysisResults.availableFields;

    // Create CSV header
    let csvContent = 'Complete KYC Records - Fully Compliant\n';
    csvContent += `Processing in batches for optimal performance...\n`;
    csvContent += 'Address Group Validation: If ANY address field has data, ALL address fields count as complete\n';
    csvContent += 'Special Field Rules: NO_OF_PTO and NO_OF_SIGNATURE with value "0" are treated as BLANK/MISSING\n';
    csvContent += 'DEFAULT Field Rule: DEFAULT field is only BLANK if ALL other fields in the record are blank\n\n';
    csvContent += headers.join(',') + ',Profile_Completeness_Percentage\n';

    let completeRecordsCount = 0;
    let processedRecords = 0;

    // Process in batches using setTimeout for non-blocking processing
    function processBatch(startIndex) {
        const endIndex = Math.min(startIndex + batchSize, currentData.length);
        let batchCsv = '';

        for (let i = startIndex; i < endIndex; i++) {
            if (analysisResults.profileCompleteness[i] === 100) {
                const row = currentData[i];
                const values = headers.map(header => {
                    const value = row[header] || '';
                    return `"${value.toString().replace(/"/g, '""')}"`;
                });
                values.push('100.0%');
                batchCsv += values.join(',') + '\n';
                completeRecordsCount++;
            }
            processedRecords++;
        }

        csvContent += batchCsv;

        // Update progress
        const progress = Math.round((processedRecords / currentData.length) * 100);
        showSuccessMessage(`🔄 Processing complete records: ${progress}% (${completeRecordsCount} complete records found)`);

        if (endIndex < currentData.length) {
            // Continue with next batch
            setTimeout(() => processBatch(endIndex), 10);
        } else {
            // Finished processing - download file
            if (completeRecordsCount === 0) {
                showErrorMessage('No complete records found. All customer profiles require additional information.');
                return;
            }

            // Update header with final count
            csvContent = csvContent.replace(
                'Processing in batches for optimal performance...',
                `Total Complete Records: ${completeRecordsCount} out of ${currentData.length}\nCompletion Rate: ${((completeRecordsCount / currentData.length) * 100).toFixed(1)}%`
            );

            const currentDate = new Date().toISOString().split('T')[0];
            const filename = `KYC_Complete_Records_Streaming_${currentDate}.csv`;
            downloadFile(csvContent, filename, 'text/csv');

            showSuccessMessage(`✅ Streaming export completed! File: ${filename} (${completeRecordsCount} fully compliant records)`);
        }
    }

    // Start processing
    processBatch(0);
}

function exportIncompleteRecordsStreaming() {
    if (!currentData || currentData.length === 0 || !analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    showSuccessMessage('🚀 Starting streaming export of incomplete KYC records...');

    // Use streaming approach with batches
    const batchSize = 5000;
    const headers = analysisResults.availableFields;

    // Create CSV header
    let csvContent = 'Incomplete KYC Records - Requiring Attention\n';
    csvContent += `Processing in batches for optimal performance...\n`;
    csvContent += 'Address Group Validation: If ANY address field has data, ALL address fields count as complete\n';
    csvContent += 'Special Field Rules: NO_OF_PTO and NO_OF_SIGNATURE with value "0" are treated as BLANK/MISSING\n';
    csvContent += 'DEFAULT Field Rule: DEFAULT field is only BLANK if ALL other fields in the record are blank\n\n';
    csvContent += headers.join(',') + ',Profile_Completeness_Percentage\n';

    let incompleteRecordsCount = 0;
    let processedRecords = 0;

    // Process in batches using setTimeout for non-blocking processing
    function processBatch(startIndex) {
        const endIndex = Math.min(startIndex + batchSize, currentData.length);
        let batchCsv = '';

        for (let i = startIndex; i < endIndex; i++) {
            if (analysisResults.profileCompleteness[i] < 100) {
                const row = currentData[i];
                const values = headers.map(header => {
                    const value = row[header] || '';
                    return `"${value.toString().replace(/"/g, '""')}"`;
                });
                values.push(`${analysisResults.profileCompleteness[i].toFixed(1)}%`);
                batchCsv += values.join(',') + '\n';
                incompleteRecordsCount++;
            }
            processedRecords++;
        }

        csvContent += batchCsv;

        // Update progress
        const progress = Math.round((processedRecords / currentData.length) * 100);
        showSuccessMessage(`🔄 Processing incomplete records: ${progress}% (${incompleteRecordsCount} incomplete records found)`);

        if (endIndex < currentData.length) {
            // Continue with next batch
            setTimeout(() => processBatch(endIndex), 10);
        } else {
            // Finished processing - download file
            if (incompleteRecordsCount === 0) {
                showSuccessMessage('🎉 Excellent! No incomplete records found. All customer profiles are complete.');
                return;
            }

            // Update header with final count
            csvContent = csvContent.replace(
                'Processing in batches for optimal performance...',
                `Total Incomplete Records: ${incompleteRecordsCount} out of ${currentData.length}\nCompletion Rate: ${(((currentData.length - incompleteRecordsCount) / currentData.length) * 100).toFixed(1)}%`
            );

            const currentDate = new Date().toISOString().split('T')[0];
            const filename = `KYC_Incomplete_Records_Streaming_${currentDate}.csv`;
            downloadFile(csvContent, filename, 'text/csv');

            showSuccessMessage(`✅ Streaming export completed! File: ${filename} (${incompleteRecordsCount} records requiring attention)`);
        }
    }

    // Start processing
    processBatch(0);
}

// Data format validation functions
function validateContactNumber(contactNo) {
    if (!contactNo || contactNo.toString().trim() === '') {
        return { isValid: true, violations: [] }; // Empty is not a format violation
    }

    const violations = [];
    const contactStr = contactNo.toString().trim();

    // Check if contains only numbers
    if (!/^\d+$/.test(contactStr)) {
        violations.push('Contains non-numeric characters');
    }

    // Enhanced phone number format validation
    const validFormats = [
        { pattern: /^\d{7}$/, description: '7-digit format' },                        // 1553557
        { pattern: /^\d{8}$/, description: '8-digit format' },                        // 15535572
        { pattern: /^\d{9}$/, description: '9-digit format' },                        // 925284320
        { pattern: /^9\d{9}$/, description: '10-digit format starting with 9' },      // 9252843200
        { pattern: /^95\d{10}$/, description: '12-digit format starting with 95' }    // 959252843200
    ];

    const isValidFormat = validFormats.some(format => format.pattern.test(contactStr));

    if (!isValidFormat) {
        violations.push(`Invalid format (${contactStr.length} digits). Valid formats: 7-digit, 8-digit, 9-digit, 10-digit starting with 9, or 12-digit starting with 95`);
    }

    return {
        isValid: violations.length === 0,
        violations: violations
    };
}

function validateNationalIdentifier(nationalId) {
    if (!nationalId || nationalId.toString().trim() === '') {
        return { isValid: true, violations: [] }; // Empty is not a format violation
    }

    const violations = [];
    const idStr = nationalId.toString().trim();

    // Enhanced pattern: [numbers]/[letters](X)-[numbers] where X can be any character
    // Example: 12/ThaGaKa(N)-011294, 12/DaGaNa(E)-008985, 12/DaGaNa(A)-017830, 12/DaGaNa(1)-017830
    const pattern = /^\d+\/[A-Za-z]+\(.+\)-\d+$/;

    if (!pattern.test(idStr)) {
        // Check specific components to provide detailed feedback
        if (!idStr.includes('/')) {
            violations.push('Missing slash (/) separator');
        }
        if (!idStr.includes('(') || !idStr.includes(')')) {
            violations.push('Missing parentheses around character indicator');
        }
        if (!idStr.includes('-')) {
            violations.push('Missing hyphen (-) separator');
        }

        // Check for proper structure components
        const parts = idStr.split('/');
        if (parts.length !== 2) {
            violations.push('Invalid structure: should have exactly one slash (/)');
        } else {
            const firstPart = parts[0];
            const secondPart = parts[1];

            if (!/^\d+$/.test(firstPart)) {
                violations.push('First part should contain only numbers');
            }

            const parenMatch = secondPart.match(/^([A-Za-z]+)\((.+)\)-(\d+)$/);
            if (!parenMatch) {
                violations.push('Second part should follow format: [letters](character)-[numbers]');
            }
        }

        // If no specific issues found, it's a general format violation
        if (violations.length === 0) {
            violations.push('Does not match required pattern: [numbers]/[letters](character)-[numbers]');
        }
    }

    return {
        isValid: violations.length === 0,
        violations: violations
    };
}

function validateAddressFormat(address, fieldName) {
    if (!address || address.toString().trim() === '') {
        return { isValid: true, violations: [] }; // Empty is not a format violation
    }

    const violations = [];
    const addressStr = address.toString().trim().toLowerCase();

    // Common Myanmar district indicators
    const districtIndicators = [
        'township', 'tsp', 'district', 'ward', 'quarter', 'street', 'road', 'avenue',
        'မြို့နယ်', 'ရပ်ကွက်', 'လမ်း', 'ရိပ်သာ', 'ခရိုင်'
    ];

    // Common Myanmar state/region indicators
    const stateIndicators = [
        'yangon', 'mandalay', 'naypyitaw', 'bago', 'magway', 'sagaing', 'tanintharyi',
        'ayeyarwady', 'mon', 'kayin', 'kayah', 'chin', 'shan', 'rakhine', 'kachin',
        'ရန်ကုန်', 'မန္တလေး', 'နေပြည်တော်', 'ပဲခူး', 'မကွေး', 'စစ်ကိုင်း', 'တနင်္သာရီ',
        'ဧရာဝတီ', 'မွန်', 'ကရင်', 'ကယား', 'ချင်း', 'ရှမ်း', 'ရခိုင်', 'ကချင်',
        'region', 'state', 'division'
    ];

    const hasDistrictInfo = districtIndicators.some(indicator =>
        addressStr.includes(indicator.toLowerCase())
    );

    const hasStateInfo = stateIndicators.some(indicator =>
        addressStr.includes(indicator.toLowerCase())
    );

    if (!hasDistrictInfo) {
        violations.push('Missing district/township information');
    }

    if (!hasStateInfo) {
        violations.push('Missing state/region information');
    }

    return {
        isValid: violations.length === 0,
        violations: violations
    };
}

// Validation functions for complete records only (skip empty fields)
function validateContactNumberCompleteOnly(contactNo) {
    if (!contactNo || contactNo.toString().trim() === '') {
        return { isValid: true, violations: [], isEmpty: true }; // Skip empty fields
    }

    const violations = [];
    const contactStr = contactNo.toString().trim();

    // Check if contains only numbers
    if (!/^\d+$/.test(contactStr)) {
        violations.push('Contains non-numeric characters');
    }

    // Enhanced phone number format validation
    const validFormats = [
        { pattern: /^\d{7}$/, description: '7-digit format' },                        // 1553557
        { pattern: /^\d{8}$/, description: '8-digit format' },                        // 15535572
        { pattern: /^\d{9}$/, description: '9-digit format' },                        // 925284320
        { pattern: /^9\d{9}$/, description: '10-digit format starting with 9' },      // 9252843200
        { pattern: /^95\d{10}$/, description: '12-digit format starting with 95' }    // 959252843200
    ];

    const isValidFormat = validFormats.some(format => format.pattern.test(contactStr));

    if (!isValidFormat) {
        violations.push(`Invalid format (${contactStr.length} digits). Valid formats: 7-digit, 8-digit, 9-digit, 10-digit starting with 9, or 12-digit starting with 95`);
    }

    return {
        isValid: violations.length === 0,
        violations: violations,
        isEmpty: false
    };
}

function validateNationalIdentifierCompleteOnly(nationalId) {
    if (!nationalId || nationalId.toString().trim() === '') {
        return { isValid: true, violations: [], isEmpty: true }; // Skip empty fields
    }

    const violations = [];
    const idStr = nationalId.toString().trim();

    // Enhanced pattern: [numbers]/[letters](X)-[numbers] where X can be any character
    // Example: 12/ThaGaKa(N)-011294, 12/DaGaNa(E)-008985, 12/DaGaNa(A)-017830, 12/DaGaNa(1)-017830
    const pattern = /^\d+\/[A-Za-z]+\(.+\)-\d+$/;

    if (!pattern.test(idStr)) {
        // Check specific components to provide detailed feedback
        if (!idStr.includes('/')) {
            violations.push('Missing slash (/) separator');
        }
        if (!idStr.includes('(') || !idStr.includes(')')) {
            violations.push('Missing parentheses around character indicator');
        }
        if (!idStr.includes('-')) {
            violations.push('Missing hyphen (-) separator');
        }

        // Check for proper structure components
        const parts = idStr.split('/');
        if (parts.length !== 2) {
            violations.push('Invalid structure: should have exactly one slash (/)');
        } else {
            const firstPart = parts[0];
            const secondPart = parts[1];

            if (!/^\d+$/.test(firstPart)) {
                violations.push('First part should contain only numbers');
            }

            const parenMatch = secondPart.match(/^([A-Za-z]+)\((.+)\)-(\d+)$/);
            if (!parenMatch) {
                violations.push('Second part should follow format: [letters](character)-[numbers]');
            }
        }

        // If no specific issues found, it's a general format violation
        if (violations.length === 0) {
            violations.push('Does not match required pattern: [numbers]/[letters](character)-[numbers]');
        }
    }

    return {
        isValid: violations.length === 0,
        violations: violations,
        isEmpty: false
    };
}

function validateAddressFormatCompleteOnly(address, fieldName) {
    if (!address || address.toString().trim() === '') {
        return { isValid: true, violations: [], isEmpty: true }; // Skip empty fields
    }

    const violations = [];
    const addressStr = address.toString().trim().toLowerCase();

    // Common Myanmar district indicators
    const districtIndicators = [
        'township', 'tsp', 'district', 'ward', 'quarter', 'street', 'road', 'avenue',
        'မြို့နယ်', 'ရပ်ကွက်', 'လမ်း', 'ရိပ်သာ', 'ခရိုင်'
    ];

    // Common Myanmar state/region indicators
    const stateIndicators = [
        'yangon', 'mandalay', 'naypyitaw', 'bago', 'magway', 'sagaing', 'tanintharyi',
        'ayeyarwady', 'mon', 'kayin', 'kayah', 'chin', 'shan', 'rakhine', 'kachin',
        'ရန်ကုန်', 'မန္တလေး', 'နေပြည်တော်', 'ပဲခူး', 'မကွေး', 'စစ်ကိုင်း', 'တနင်္သာရီ',
        'ဧရာဝတီ', 'မွန်', 'ကရင်', 'ကယား', 'ချင်း', 'ရှမ်း', 'ရခိုင်', 'ကချင်',
        'region', 'state', 'division'
    ];

    const hasDistrictInfo = districtIndicators.some(indicator =>
        addressStr.includes(indicator.toLowerCase())
    );

    const hasStateInfo = stateIndicators.some(indicator =>
        addressStr.includes(indicator.toLowerCase())
    );

    if (!hasDistrictInfo) {
        violations.push('Missing district/township information');
    }

    if (!hasStateInfo) {
        violations.push('Missing state/region information');
    }

    return {
        isValid: violations.length === 0,
        violations: violations,
        isEmpty: false
    };
}

// Validation functions for incomplete records (validate all populated fields)
function validateContactNumberIncompleteOnly(contactNo) {
    if (!contactNo || contactNo.toString().trim() === '') {
        return { isValid: true, violations: [], isEmpty: true }; // Track empty but don't flag as violation
    }

    const violations = [];
    const contactStr = contactNo.toString().trim();

    // Check if contains only numbers
    if (!/^\d+$/.test(contactStr)) {
        violations.push('Contains non-numeric characters');
    }

    // Enhanced phone number format validation
    const validFormats = [
        { pattern: /^\d{7}$/, description: '7-digit format' },                        // 1553557
        { pattern: /^\d{8}$/, description: '8-digit format' },                        // 15535572
        { pattern: /^\d{9}$/, description: '9-digit format' },                        // 925284320
        { pattern: /^9\d{9}$/, description: '10-digit format starting with 9' },      // 9252843200
        { pattern: /^95\d{10}$/, description: '12-digit format starting with 95' }    // 959252843200
    ];

    const isValidFormat = validFormats.some(format => format.pattern.test(contactStr));

    if (!isValidFormat) {
        violations.push(`Invalid format (${contactStr.length} digits). Valid formats: 7-digit, 8-digit, 9-digit, 10-digit starting with 9, or 12-digit starting with 95`);
    }

    return {
        isValid: violations.length === 0,
        violations: violations,
        isEmpty: false
    };
}

function validateNationalIdentifierIncompleteOnly(nationalId) {
    if (!nationalId || nationalId.toString().trim() === '') {
        return { isValid: true, violations: [], isEmpty: true }; // Track empty but don't flag as violation
    }

    const violations = [];
    const idStr = nationalId.toString().trim();

    // Enhanced pattern: [numbers]/[letters](X)-[numbers] where X can be any character
    // Example: 12/ThaGaKa(N)-011294, 12/DaGaNa(E)-008985, 12/DaGaNa(A)-017830, 12/DaGaNa(1)-017830
    const pattern = /^\d+\/[A-Za-z]+\(.+\)-\d+$/;

    if (!pattern.test(idStr)) {
        // Check specific components to provide detailed feedback
        if (!idStr.includes('/')) {
            violations.push('Missing slash (/) separator');
        }
        if (!idStr.includes('(') || !idStr.includes(')')) {
            violations.push('Missing parentheses around character indicator');
        }
        if (!idStr.includes('-')) {
            violations.push('Missing hyphen (-) separator');
        }

        // Check for proper structure components
        const parts = idStr.split('/');
        if (parts.length !== 2) {
            violations.push('Invalid structure: should have exactly one slash (/)');
        } else {
            const firstPart = parts[0];
            const secondPart = parts[1];

            if (!/^\d+$/.test(firstPart)) {
                violations.push('First part should contain only numbers');
            }

            const parenMatch = secondPart.match(/^([A-Za-z]+)\((.+)\)-(\d+)$/);
            if (!parenMatch) {
                violations.push('Second part should follow format: [letters](character)-[numbers]');
            }
        }

        // If no specific issues found, it's a general format violation
        if (violations.length === 0) {
            violations.push('Does not match required pattern: [numbers]/[letters](character)-[numbers]');
        }
    }

    return {
        isValid: violations.length === 0,
        violations: violations,
        isEmpty: false
    };
}

function validateAddressFormatIncompleteOnly(address, fieldName) {
    if (!address || address.toString().trim() === '') {
        return { isValid: true, violations: [], isEmpty: true }; // Track empty but don't flag as violation
    }

    const violations = [];
    const addressStr = address.toString().trim().toLowerCase();

    // Common Myanmar district indicators
    const districtIndicators = [
        'township', 'tsp', 'district', 'ward', 'quarter', 'street', 'road', 'avenue',
        'မြို့နယ်', 'ရပ်ကွက်', 'လမ်း', 'ရိပ်သာ', 'ခရိုင်'
    ];

    // Common Myanmar state/region indicators
    const stateIndicators = [
        'yangon', 'mandalay', 'naypyitaw', 'bago', 'magway', 'sagaing', 'tanintharyi',
        'ayeyarwady', 'mon', 'kayin', 'kayah', 'chin', 'shan', 'rakhine', 'kachin',
        'ရန်ကုန်', 'မန္တလေး', 'နေပြည်တော်', 'ပဲခူး', 'မကွေး', 'စစ်ကိုင်း', 'တနင်္သာရီ',
        'ဧရာဝတီ', 'မွန်', 'ကရင်', 'ကယား', 'ချင်း', 'ရှမ်း', 'ရခိုင်', 'ကချင်',
        'region', 'state', 'division'
    ];

    const hasDistrictInfo = districtIndicators.some(indicator =>
        addressStr.includes(indicator.toLowerCase())
    );

    const hasStateInfo = stateIndicators.some(indicator =>
        addressStr.includes(indicator.toLowerCase())
    );

    if (!hasDistrictInfo) {
        violations.push('Missing district/township information');
    }

    if (!hasStateInfo) {
        violations.push('Missing state/region information');
    }

    return {
        isValid: violations.length === 0,
        violations: violations,
        isEmpty: false
    };
}

// Field selection dialog for CSV exports
function showFieldSelectionDialogCSV() {
    if (!analysisResults || !analysisResults.fieldAnalysis) {
        showErrorMessage('Analysis results not available. Please analyze data first.');
        return;
    }

    // Create modal HTML for field selection
    const modalHtml = `
        <div id="csvFieldSelectionModal" class="modal-overlay">
            <div class="modal-content field-selection-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-file-csv"></i> Select Fields for CSV Export</h3>
                    <button class="modal-close" onclick="closeCsvFieldSelectionModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="selection-controls">
                        <div class="bulk-actions">
                            <button class="btn btn-sm btn-secondary" onclick="selectAllCsvFields()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearAllCsvFields()">
                                <i class="fas fa-square"></i> Clear All
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="selectHighRiskCsvFields()">
                                <i class="fas fa-exclamation-triangle"></i> High Risk Only
                            </button>
                        </div>
                        <div class="export-type-selection">
                            <label><input type="radio" name="csvExportType" value="blank" checked> Export BLANK records only</label>
                            <label><input type="radio" name="csvExportType" value="complete"> Export COMPLETE records only</label>
                            <label><input type="radio" name="csvExportType" value="all"> Export ALL records</label>
                        </div>
                    </div>
                    <div class="field-grid" id="csvFieldGrid">
                        <!-- Fields will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeCsvFieldSelectionModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="exportSelectedFieldsCSV()">
                        <i class="fas fa-download"></i> Export Selected Fields
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Populate field grid
    populateCsvFieldGrid();

    // Show modal
    document.getElementById('csvFieldSelectionModal').style.display = 'flex';
}

function populateCsvFieldGrid() {
    const fieldGrid = document.getElementById('csvFieldGrid');
    const fields = Object.entries(analysisResults.fieldAnalysis)
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage); // Sort by risk level

    fieldGrid.innerHTML = fields.map(([fieldName, analysis]) => {
        const riskClass = analysis.riskLevel.toLowerCase().replace(' ', '-');
        const blankCount = analysis.blankCount.toLocaleString();

        return `
            <div class="field-item ${riskClass}">
                <label class="field-checkbox">
                    <input type="checkbox" name="csvSelectedFields" value="${fieldName}">
                    <div class="field-info">
                        <div class="field-name">${fieldName}</div>
                        <div class="field-stats">
                            <span class="blank-count">${blankCount} blank</span>
                            <span class="risk-badge ${riskClass}">${analysis.riskLevel}</span>
                        </div>
                    </div>
                </label>
            </div>
        `;
    }).join('');
}

function selectAllCsvFields() {
    const checkboxes = document.querySelectorAll('input[name="csvSelectedFields"]');
    checkboxes.forEach(cb => cb.checked = true);
}

function clearAllCsvFields() {
    const checkboxes = document.querySelectorAll('input[name="csvSelectedFields"]');
    checkboxes.forEach(cb => cb.checked = false);
}

function selectHighRiskCsvFields() {
    const checkboxes = document.querySelectorAll('input[name="csvSelectedFields"]');
    checkboxes.forEach(cb => {
        const fieldName = cb.value;
        const analysis = analysisResults.fieldAnalysis[fieldName];
        cb.checked = analysis && (analysis.riskLevel === 'URGENT' || analysis.riskLevel === 'HIGH');
    });
}

function closeCsvFieldSelectionModal() {
    const modal = document.getElementById('csvFieldSelectionModal');
    if (modal) {
        modal.remove();
    }
}

function exportSelectedFieldsCSV() {
    const selectedFields = Array.from(document.querySelectorAll('input[name="csvSelectedFields"]:checked'))
        .map(cb => cb.value);

    if (selectedFields.length === 0) {
        showErrorMessage('Please select at least one field to export.');
        return;
    }

    const exportType = document.querySelector('input[name="csvExportType"]:checked').value;

    // Close modal
    closeCsvFieldSelectionModal();

    // Show progress
    showSuccessMessage(`🚀 Starting streaming export of ${selectedFields.length} selected fields (${exportType} records)...`);

    // Use streaming approach for selected fields
    const batchSize = 5000;
    let csvContent = '';
    let exportedRecordsCount = 0;
    let processedRecords = 0;

    // Create CSV header based on export type
    if (exportType === 'blank') {
        csvContent = `Selected Fields Export - BLANK Records Only\n`;
        csvContent += `Fields: ${selectedFields.join(', ')}\n`;
        csvContent += `Export Type: Records with BLANK values in selected fields\n`;
    } else if (exportType === 'complete') {
        csvContent = `Selected Fields Export - COMPLETE Records Only\n`;
        csvContent += `Fields: ${selectedFields.join(', ')}\n`;
        csvContent += `Export Type: Records with COMPLETE values in selected fields\n`;
    } else {
        csvContent = `Selected Fields Export - ALL Records\n`;
        csvContent += `Fields: ${selectedFields.join(', ')}\n`;
        csvContent += `Export Type: All records regardless of field completion status\n`;
    }

    csvContent += `Processing in batches for optimal performance...\n`;
    csvContent += 'Address Group Validation: If ANY address field has data, ALL address fields count as complete\n';
    csvContent += 'Special Field Rules: NO_OF_PTO and NO_OF_SIGNATURE with value "0" are treated as BLANK/MISSING\n';
    csvContent += 'DEFAULT Field Rule: DEFAULT field is only BLANK if ALL other fields in the record are blank\n\n';

    // Add context columns + selected fields + field status columns
    const contextColumns = [
        'CONTACT_NO', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'NO_OF_PTO', 'OCCUPATION',
        'INDUSTRY_SECTOR', 'CUSTOMER_SEGMENT', 'CUSTOMER_SEGMENT_UDF', 'INCOME_LEVEL',
        'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'DEFAULT', 'OFFICE_ADDRESS',
        'EMPLOYMENTSTATUS', 'DATE_OF_BIRTH', 'NATIONALITY', 'FATHERNAME', 'NO_OF_SIGNATURE'
    ];

    const availableContextColumns = contextColumns.filter(col =>
        analysisResults.availableFields.includes(col) && !selectedFields.includes(col)
    );

    const csvHeaders = ['CIF', ...availableContextColumns, ...selectedFields];

    // Add field status columns for each selected field
    selectedFields.forEach(field => {
        csvHeaders.push(`${field}_STATUS`);
    });

    csvContent += csvHeaders.join(',') + '\n';

    // Process in batches using setTimeout for non-blocking processing
    function processBatch(startIndex) {
        const endIndex = Math.min(startIndex + batchSize, currentData.length);
        let batchCsv = '';

        for (let i = startIndex; i < endIndex; i++) {
            const row = currentData[i];
            let includeRecord = false;

            // Determine if record should be included based on export type
            if (exportType === 'blank') {
                // Include if ANY selected field is blank
                includeRecord = selectedFields.some(field => isFieldBlank(row[field], field));
            } else if (exportType === 'complete') {
                // Include if ALL selected fields are complete
                includeRecord = selectedFields.every(field => !isFieldBlank(row[field], field));
            } else {
                // Include all records
                includeRecord = true;
            }

            if (includeRecord) {
                const values = [];

                // Add CIF
                values.push(`"${(row['CIF'] || '').toString().replace(/"/g, '""')}"`);

                // Add context columns
                availableContextColumns.forEach(col => {
                    const value = row[col] || '';
                    values.push(`"${value.toString().replace(/"/g, '""')}"`);
                });

                // Add selected field values
                selectedFields.forEach(field => {
                    const value = row[field] || '';
                    values.push(`"${value.toString().replace(/"/g, '""')}"`);
                });

                // Add field status columns
                selectedFields.forEach(field => {
                    const status = isFieldBlank(row[field], field) ? 'BLANK' : 'COMPLETE';
                    values.push(status);
                });

                batchCsv += values.join(',') + '\n';
                exportedRecordsCount++;
            }
            processedRecords++;
        }

        csvContent += batchCsv;

        // Update progress
        const progress = Math.round((processedRecords / currentData.length) * 100);
        showSuccessMessage(`🔄 Processing selected fields: ${progress}% (${exportedRecordsCount} matching records found)`);

        if (endIndex < currentData.length) {
            // Continue with next batch
            setTimeout(() => processBatch(endIndex), 10);
        } else {
            // Finished processing - download file
            if (exportedRecordsCount === 0) {
                showErrorMessage(`No records found matching the ${exportType} criteria for selected fields.`);
                return;
            }

            // Update header with final count
            csvContent = csvContent.replace(
                'Processing in batches for optimal performance...',
                `Total Exported Records: ${exportedRecordsCount} out of ${currentData.length} (${((exportedRecordsCount / currentData.length) * 100).toFixed(1)}%)`
            );

            const currentDate = new Date().toISOString().split('T')[0];
            const fieldNames = selectedFields.slice(0, 3).join('_'); // Use first 3 field names
            const filename = `KYC_Selected_Fields_${fieldNames}_${exportType}_${currentDate}.csv`;
            downloadFile(csvContent, filename, 'text/csv');

            showSuccessMessage(`✅ Selected fields export completed! File: ${filename} (${exportedRecordsCount} records, ${selectedFields.length} fields)`);
        }
    }

    // Start processing
    processBatch(0);
}

// Export records with data format violations
function exportDataFormatViolations() {
    if (!currentData || currentData.length === 0 || !analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    showSuccessMessage('🔍 Analyzing data format violations...');

    // Analyze all records for format violations
    const violationRecords = [];
    let totalViolations = 0;

    currentData.forEach((row, index) => {
        const violations = {
            recordIndex: index + 1,
            cif: row.CIF || '',
            fullName: row.FULL_NAME || '',
            contactViolations: [],
            nationalIdViolations: [],
            addressViolations: [],
            hasViolations: false
        };

        // Validate CONTACT_NO
        if (row.CONTACT_NO) {
            const contactValidation = validateContactNumber(row.CONTACT_NO);
            if (!contactValidation.isValid) {
                violations.contactViolations = contactValidation.violations;
                violations.hasViolations = true;
            }
        }

        // Validate NATIONAL_IDENTIFIER
        if (row.NATIONAL_IDENTIFIER) {
            const nationalIdValidation = validateNationalIdentifier(row.NATIONAL_IDENTIFIER);
            if (!nationalIdValidation.isValid) {
                violations.nationalIdViolations = nationalIdValidation.violations;
                violations.hasViolations = true;
            }
        }

        // Validate address fields
        const addressFields = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS'];
        addressFields.forEach(field => {
            if (row[field]) {
                const addressValidation = validateAddressFormat(row[field], field);
                if (!addressValidation.isValid) {
                    violations.addressViolations.push({
                        field: field,
                        violations: addressValidation.violations
                    });
                    violations.hasViolations = true;
                }
            }
        });

        // Add record if it has violations
        if (violations.hasViolations) {
            violationRecords.push({
                ...violations,
                originalRow: row
            });
            totalViolations++;
        }
    });

    if (violationRecords.length === 0) {
        showSuccessMessage('🎉 Excellent! No data format violations found in critical fields.');
        return;
    }

    // Generate CSV content
    let csv = 'KYC Data Format Violations Report\n';
    csv += `Analysis Date: ${new Date().toLocaleDateString()}\n`;
    csv += `Total Records Analyzed: ${currentData.length.toLocaleString()}\n`;
    csv += `Records with Format Violations: ${violationRecords.length.toLocaleString()}\n`;
    csv += `Violation Rate: ${((violationRecords.length / currentData.length) * 100).toFixed(2)}%\n\n`;

    csv += 'VALIDATION RULES APPLIED:\n';
    csv += '1. CONTACT_NO: Must contain only numbers in specific formats:\n';
    csv += '   - 7-digit format (e.g., 1553557)\n';
    csv += '   - 8-digit format (e.g., 15535572)\n';
    csv += '   - 9-digit format (e.g., 925284320)\n';
    csv += '   - 10-digit format starting with 9 (e.g., 9252843200)\n';
    csv += '   - 12-digit format starting with 95 (e.g., 959252843200)\n';
    csv += '2. NATIONAL_IDENTIFIER: Must follow pattern [numbers]/[letters](character)-[numbers]\n';
    csv += '   - Any character allowed in parentheses (N, E, A, 1, etc.)\n';
    csv += '3. Address Fields: Should contain district/township and state/region information\n\n';

    // CSV headers
    const headers = [
        'Record_Index', 'CIF', 'Full_Name', 'CONTACT_NO', 'Contact_Violations',
        'NATIONAL_IDENTIFIER', 'National_ID_Violations',
        'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS', 'Address_Violations',
        'Total_Violation_Types'
    ];

    csv += headers.join(',') + '\n';

    // Add violation records
    violationRecords.forEach(record => {
        const values = [];

        values.push(record.recordIndex);
        values.push(`"${(record.cif || '').toString().replace(/"/g, '""')}"`);
        values.push(`"${(record.fullName || '').toString().replace(/"/g, '""')}"`);

        // Contact number and violations
        values.push(`"${(record.originalRow.CONTACT_NO || '').toString().replace(/"/g, '""')}"`);
        values.push(`"${record.contactViolations.join('; ')}"`);

        // National ID and violations
        values.push(`"${(record.originalRow.NATIONAL_IDENTIFIER || '').toString().replace(/"/g, '""')}"`);
        values.push(`"${record.nationalIdViolations.join('; ')}"`);

        // Address fields
        values.push(`"${(record.originalRow.HOME_ADDRESS || '').toString().replace(/"/g, '""')}"`);
        values.push(`"${(record.originalRow.MAILING_ADDRESS || '').toString().replace(/"/g, '""')}"`);
        values.push(`"${(record.originalRow.ADD01 || '').toString().replace(/"/g, '""')}"`);
        values.push(`"${(record.originalRow.OFFICE_ADDRESS || '').toString().replace(/"/g, '""')}"`);

        // Address violations summary
        const addressViolationSummary = record.addressViolations.map(av =>
            `${av.field}: ${av.violations.join(', ')}`
        ).join('; ');
        values.push(`"${addressViolationSummary}"`);

        // Total violation types
        let violationTypes = 0;
        if (record.contactViolations.length > 0) violationTypes++;
        if (record.nationalIdViolations.length > 0) violationTypes++;
        if (record.addressViolations.length > 0) violationTypes++;
        values.push(violationTypes);

        csv += values.join(',') + '\n';
    });

    // Download the file
    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `KYC_Data_Format_Violations_${currentDate}.csv`;
    downloadFile(csv, filename, 'text/csv');

    showSuccessMessage(`✅ Data format violations exported! File: ${filename} (${violationRecords.length} records with violations)`);
}

// Export format violations for complete records only (skip empty fields) - Streaming Version
function exportFormatViolationsCompleteRecords() {
    if (!currentData || currentData.length === 0 || !analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    showSuccessMessage('🚀 Starting streaming analysis of format violations in complete records...');

    // First, identify complete records indices for streaming processing
    const completeRecordIndices = [];
    currentData.forEach((row, index) => {
        if (analysisResults.profileCompleteness[index] === 100) {
            completeRecordIndices.push(index);
        }
    });

    if (completeRecordIndices.length === 0) {
        showErrorMessage('No complete records found. All customer profiles require additional information.');
        return;
    }

    // Streaming processing setup
    const batchSize = 2000; // Process 2000 records at a time
    let processedRecords = 0;
    let violationRecordsCount = 0;
    let csvContent = '';

    // Create CSV header
    csvContent = 'KYC Format Violations Report - Complete Records Only\n';
    csvContent += `Analysis Date: ${new Date().toLocaleDateString()}\n`;
    csvContent += `Total Complete Records to Analyze: ${completeRecordIndices.length.toLocaleString()}\n`;
    csvContent += 'Processing in batches for optimal performance...\n\n';

    csvContent += 'ANALYSIS FOCUS: Data Quality Issues in Complete Records\n';
    csvContent += 'This report excludes empty/blank fields and focuses on incorrectly formatted data\n';
    csvContent += 'that requires different remediation approaches than missing data.\n\n';

    csvContent += 'VALIDATION RULES APPLIED (Non-Empty Fields Only):\n';
    csvContent += '1. CONTACT_NO: Must contain only numbers in specific formats:\n';
    csvContent += '   - 7-digit format (e.g., 1553557)\n';
    csvContent += '   - 8-digit format (e.g., 15535572)\n';
    csvContent += '   - 9-digit format (e.g., 925284320)\n';
    csvContent += '   - 10-digit format starting with 9 (e.g., 9252843200)\n';
    csvContent += '   - 12-digit format starting with 95 (e.g., 959252843200)\n';
    csvContent += '2. NATIONAL_IDENTIFIER: Must follow pattern [numbers]/[letters](character)-[numbers]\n';
    csvContent += '   - Any character allowed in parentheses (N, E, A, 1, etc.)\n';
    csvContent += '3. Address Fields: Should contain district/township and state/region information\n\n';

    // CSV headers
    const headers = [
        'Record_Index', 'CIF', 'Full_Name', 'Completeness_Percentage',
        'CONTACT_NO', 'Contact_Format_Issues',
        'NATIONAL_IDENTIFIER', 'National_ID_Format_Issues',
        'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS', 'Address_Format_Issues',
        'Total_Format_Issue_Types', 'Remediation_Priority'
    ];

    csvContent += headers.join(',') + '\n';

    // Streaming batch processing function
    function processBatch(startIndex) {
        const endIndex = Math.min(startIndex + batchSize, completeRecordIndices.length);
        let batchCsv = '';

        for (let i = startIndex; i < endIndex; i++) {
            const recordIndex = completeRecordIndices[i];
            const row = currentData[recordIndex];

            const violations = {
                recordIndex: recordIndex + 1,
                cif: row.CIF || '',
                fullName: row.FULL_NAME || '',
                contactViolations: [],
                nationalIdViolations: [],
                addressViolations: [],
                hasViolations: false,
                completenessPercentage: analysisResults.profileCompleteness[recordIndex]
            };

            // Validate CONTACT_NO (only if not empty)
            const contactValidation = validateContactNumberCompleteOnly(row.CONTACT_NO);
            if (!contactValidation.isEmpty && !contactValidation.isValid) {
                violations.contactViolations = contactValidation.violations;
                violations.hasViolations = true;
            }

            // Validate NATIONAL_IDENTIFIER (only if not empty)
            const nationalIdValidation = validateNationalIdentifierCompleteOnly(row.NATIONAL_IDENTIFIER);
            if (!nationalIdValidation.isEmpty && !nationalIdValidation.isValid) {
                violations.nationalIdViolations = nationalIdValidation.violations;
                violations.hasViolations = true;
            }

            // Validate address fields (only if not empty)
            const addressFields = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS'];
            addressFields.forEach(field => {
                const addressValidation = validateAddressFormatCompleteOnly(row[field], field);
                if (!addressValidation.isEmpty && !addressValidation.isValid) {
                    violations.addressViolations.push({
                        field: field,
                        violations: addressValidation.violations
                    });
                    violations.hasViolations = true;
                }
            });

            // Add record to CSV if it has violations
            if (violations.hasViolations) {
                const values = [];

                values.push(violations.recordIndex);
                values.push(`"${(violations.cif || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(violations.fullName || '').toString().replace(/"/g, '""')}"`);
                values.push(`${violations.completenessPercentage.toFixed(1)}%`);

                // Contact number and violations
                values.push(`"${(row.CONTACT_NO || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${violations.contactViolations.join('; ')}"`);

                // National ID and violations
                values.push(`"${(row.NATIONAL_IDENTIFIER || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${violations.nationalIdViolations.join('; ')}"`);

                // Address fields
                values.push(`"${(row.HOME_ADDRESS || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(row.MAILING_ADDRESS || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(row.ADD01 || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(row.OFFICE_ADDRESS || '').toString().replace(/"/g, '""')}"`);

                // Address violations summary
                const addressViolationSummary = violations.addressViolations.map(av =>
                    `${av.field}: ${av.violations.join(', ')}`
                ).join('; ');
                values.push(`"${addressViolationSummary}"`);

                // Total violation types
                let violationTypes = 0;
                if (violations.contactViolations.length > 0) violationTypes++;
                if (violations.nationalIdViolations.length > 0) violationTypes++;
                if (violations.addressViolations.length > 0) violationTypes++;
                values.push(violationTypes);

                // Remediation priority based on violation types
                let priority = 'LOW';
                if (violationTypes >= 3) priority = 'HIGH';
                else if (violationTypes >= 2) priority = 'MEDIUM';
                values.push(priority);

                batchCsv += values.join(',') + '\n';
                violationRecordsCount++;
            }
            processedRecords++;
        }

        csvContent += batchCsv;

        // Update progress
        const progress = Math.round((processedRecords / completeRecordIndices.length) * 100);
        showSuccessMessage(`🔄 Processing complete records: ${progress}% (${violationRecordsCount} format violations found)`);

        // Continue processing or finish
        if (endIndex < completeRecordIndices.length) {
            // Continue with next batch after a small delay to prevent UI blocking
            setTimeout(() => processBatch(endIndex), 10);
        } else {
            // Processing complete
            finishExport();
        }
    }

    function finishExport() {
        if (violationRecordsCount === 0) {
            showSuccessMessage(`🎉 Excellent! No format violations found in ${completeRecordIndices.length.toLocaleString()} complete records. All populated fields follow proper formatting standards.`);
            return;
        }

        // Add summary statistics at the end
        csvContent += '\n\nSUMMARY STATISTICS:\n';
        csvContent += `Total Records in Dataset: ${currentData.length.toLocaleString()}\n`;
        csvContent += `Complete Records (100%): ${completeRecordIndices.length.toLocaleString()}\n`;
        csvContent += `Complete Records with Format Issues: ${violationRecordsCount.toLocaleString()}\n`;
        csvContent += `Format Violation Rate (Complete Records): ${((violationRecordsCount / completeRecordIndices.length) * 100).toFixed(2)}%\n`;
        csvContent += `Data Quality Rate: ${(((completeRecordIndices.length - violationRecordsCount) / completeRecordIndices.length) * 100).toFixed(2)}%\n`;

        // Update header with final count
        csvContent = csvContent.replace(
            'Processing in batches for optimal performance...',
            `Complete Records with Format Violations: ${violationRecordsCount.toLocaleString()}`
        );

        const currentDate = new Date().toISOString().split('T')[0];
        const filename = `KYC_Format_Violations_Complete_Records_Streaming_${currentDate}.csv`;
        downloadFile(csvContent, filename, 'text/csv');

        showSuccessMessage(`✅ Streaming export completed! File: ${filename} (${violationRecordsCount} out of ${completeRecordIndices.length} complete records have format issues)`);
    }

    // Start processing
    processBatch(0);
}

// Export format violations for incomplete records only (validate all populated fields) - Streaming Version
function exportFormatViolationsIncompleteRecords() {
    if (!currentData || currentData.length === 0 || !analysisResults || !analysisResults.profileCompleteness) {
        showErrorMessage('No data available. Please upload and analyze data first.');
        return;
    }

    showSuccessMessage('🚀 Starting streaming analysis of format violations in incomplete records...');

    // First, identify incomplete records indices for streaming processing
    const incompleteRecordIndices = [];
    currentData.forEach((row, index) => {
        if (analysisResults.profileCompleteness[index] < 100) {
            incompleteRecordIndices.push(index);
        }
    });

    if (incompleteRecordIndices.length === 0) {
        showErrorMessage('No incomplete records found. All customer profiles are 100% complete.');
        return;
    }

    // Streaming processing setup
    const batchSize = 2000; // Process 2000 records at a time
    let processedRecords = 0;
    let violationRecordsCount = 0;
    let csvContent = '';

    // Create CSV header
    csvContent = 'KYC Format Violations Report - Incomplete Records Only\n';
    csvContent += `Analysis Date: ${new Date().toLocaleDateString()}\n`;
    csvContent += `Total Incomplete Records to Analyze: ${incompleteRecordIndices.length.toLocaleString()}\n`;
    csvContent += 'Processing in batches for optimal performance...\n\n';

    csvContent += 'ANALYSIS FOCUS: Data Quality Issues in Incomplete Records\n';
    csvContent += 'This report analyzes format violations in records with completeness < 100%\n';
    csvContent += 'and validates all populated fields to identify formatting issues requiring correction.\n\n';

    csvContent += 'VALIDATION RULES APPLIED (Populated Fields Only):\n';
    csvContent += '1. CONTACT_NO: Must contain only numbers in specific formats:\n';
    csvContent += '   - 7-digit format (e.g., 1553557)\n';
    csvContent += '   - 8-digit format (e.g., 15535572)\n';
    csvContent += '   - 9-digit format (e.g., 925284320)\n';
    csvContent += '   - 10-digit format starting with 9 (e.g., 9252843200)\n';
    csvContent += '   - 12-digit format starting with 95 (e.g., 959252843200)\n';
    csvContent += '2. NATIONAL_IDENTIFIER: Must follow pattern [numbers]/[letters](character)-[numbers]\n';
    csvContent += '   - Any character allowed in parentheses (N, E, A, 1, etc.)\n';
    csvContent += '3. Address Fields: Should contain district/township and state/region information\n\n';

    // CSV headers
    const headers = [
        'Record_Index', 'CIF', 'Full_Name', 'Completeness_Percentage',
        'CONTACT_NO', 'Contact_Format_Issues',
        'NATIONAL_IDENTIFIER', 'National_ID_Format_Issues',
        'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS', 'Address_Format_Issues',
        'Total_Format_Issue_Types', 'Remediation_Priority', 'Completion_Status'
    ];

    csvContent += headers.join(',') + '\n';

    // Streaming batch processing function
    function processBatch(startIndex) {
        const endIndex = Math.min(startIndex + batchSize, incompleteRecordIndices.length);
        let batchCsv = '';

        for (let i = startIndex; i < endIndex; i++) {
            const recordIndex = incompleteRecordIndices[i];
            const row = currentData[recordIndex];

            const violations = {
                recordIndex: recordIndex + 1,
                cif: row.CIF || '',
                fullName: row.FULL_NAME || '',
                contactViolations: [],
                nationalIdViolations: [],
                addressViolations: [],
                hasViolations: false,
                completenessPercentage: analysisResults.profileCompleteness[recordIndex]
            };

            // Validate CONTACT_NO (only if populated)
            const contactValidation = validateContactNumberIncompleteOnly(row.CONTACT_NO);
            if (!contactValidation.isEmpty && !contactValidation.isValid) {
                violations.contactViolations = contactValidation.violations;
                violations.hasViolations = true;
            }

            // Validate NATIONAL_IDENTIFIER (only if populated)
            const nationalIdValidation = validateNationalIdentifierIncompleteOnly(row.NATIONAL_IDENTIFIER);
            if (!nationalIdValidation.isEmpty && !nationalIdValidation.isValid) {
                violations.nationalIdViolations = nationalIdValidation.violations;
                violations.hasViolations = true;
            }

            // Validate address fields (only if populated)
            const addressFields = ['HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'OFFICE_ADDRESS'];
            addressFields.forEach(field => {
                const addressValidation = validateAddressFormatIncompleteOnly(row[field], field);
                if (!addressValidation.isEmpty && !addressValidation.isValid) {
                    violations.addressViolations.push({
                        field: field,
                        violations: addressValidation.violations
                    });
                    violations.hasViolations = true;
                }
            });

            // Add record to CSV if it has violations
            if (violations.hasViolations) {
                const values = [];

                values.push(violations.recordIndex);
                values.push(`"${(violations.cif || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(violations.fullName || '').toString().replace(/"/g, '""')}"`);
                values.push(`${violations.completenessPercentage.toFixed(1)}%`);

                // Contact number and violations
                values.push(`"${(row.CONTACT_NO || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${violations.contactViolations.join('; ')}"`);

                // National ID and violations
                values.push(`"${(row.NATIONAL_IDENTIFIER || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${violations.nationalIdViolations.join('; ')}"`);

                // Address fields
                values.push(`"${(row.HOME_ADDRESS || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(row.MAILING_ADDRESS || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(row.ADD01 || '').toString().replace(/"/g, '""')}"`);
                values.push(`"${(row.OFFICE_ADDRESS || '').toString().replace(/"/g, '""')}"`);

                // Address violations summary
                const addressViolationSummary = violations.addressViolations.map(av =>
                    `${av.field}: ${av.violations.join(', ')}`
                ).join('; ');
                values.push(`"${addressViolationSummary}"`);

                // Total violation types
                let violationTypes = 0;
                if (violations.contactViolations.length > 0) violationTypes++;
                if (violations.nationalIdViolations.length > 0) violationTypes++;
                if (violations.addressViolations.length > 0) violationTypes++;
                values.push(violationTypes);

                // Remediation priority based on violation types and completeness
                let priority = 'LOW';
                if (violationTypes >= 3 || violations.completenessPercentage < 50) priority = 'HIGH';
                else if (violationTypes >= 2 || violations.completenessPercentage < 75) priority = 'MEDIUM';
                values.push(priority);

                // Completion status
                let completionStatus = 'NEEDS_DATA_AND_FORMAT_FIX';
                if (violations.completenessPercentage >= 75) completionStatus = 'MOSTLY_COMPLETE_FORMAT_ISSUES';
                else if (violations.completenessPercentage >= 50) completionStatus = 'PARTIALLY_COMPLETE_FORMAT_ISSUES';
                values.push(completionStatus);

                batchCsv += values.join(',') + '\n';
                violationRecordsCount++;
            }
            processedRecords++;
        }

        csvContent += batchCsv;

        // Update progress
        const progress = Math.round((processedRecords / incompleteRecordIndices.length) * 100);
        showSuccessMessage(`🔄 Processing incomplete records: ${progress}% (${violationRecordsCount} format violations found)`);

        // Continue processing or finish
        if (endIndex < incompleteRecordIndices.length) {
            // Continue with next batch after a small delay to prevent UI blocking
            setTimeout(() => processBatch(endIndex), 10);
        } else {
            // Processing complete
            finishExport();
        }
    }

    function finishExport() {
        if (violationRecordsCount === 0) {
            showSuccessMessage(`🎉 Excellent! No format violations found in ${incompleteRecordIndices.length.toLocaleString()} incomplete records. All populated fields follow proper formatting standards.`);
            return;
        }

        // Add summary statistics at the end
        csvContent += '\n\nSUMMARY STATISTICS:\n';
        csvContent += `Total Records in Dataset: ${currentData.length.toLocaleString()}\n`;
        csvContent += `Incomplete Records (< 100%): ${incompleteRecordIndices.length.toLocaleString()}\n`;
        csvContent += `Incomplete Records with Format Issues: ${violationRecordsCount.toLocaleString()}\n`;
        csvContent += `Format Violation Rate (Incomplete Records): ${((violationRecordsCount / incompleteRecordIndices.length) * 100).toFixed(2)}%\n`;
        csvContent += `Data Quality Rate (Incomplete Records): ${(((incompleteRecordIndices.length - violationRecordsCount) / incompleteRecordIndices.length) * 100).toFixed(2)}%\n`;

        // Update header with final count
        csvContent = csvContent.replace(
            'Processing in batches for optimal performance...',
            `Incomplete Records with Format Violations: ${violationRecordsCount.toLocaleString()}`
        );

        const currentDate = new Date().toISOString().split('T')[0];
        const filename = `KYC_Format_Violations_Incomplete_Records_Streaming_${currentDate}.csv`;
        downloadFile(csvContent, filename, 'text/csv');

        showSuccessMessage(`✅ Streaming export completed! File: ${filename} (${violationRecordsCount} out of ${incompleteRecordIndices.length} incomplete records have format issues)`);
    }

    // Start processing
    processBatch(0);
}

function exportSummaryReport() {
    const summaryContent = generateExecutiveSummaryReport();
    downloadFile(summaryContent, 'kyc-executive-summary.html', 'text/html');
}

function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

function generateExecutiveSummaryReport() {
    const currentDate = new Date().toLocaleDateString();
    const currentTime = new Date().toLocaleTimeString();

    // Calculate key metrics
    const highRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'HIGH');

    const mediumRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'MEDIUM');

    const criticalFieldsAnalysis = criticalFields
        .filter(field => analysisResults.fieldAnalysis[field])
        .map(field => ({
            field,
            ...analysisResults.fieldAnalysis[field]
        }));

    const averageCompleteness = analysisResults.completionRate;
    const complianceStatus = averageCompleteness >= 90 ? 'EXCELLENT' :
                           averageCompleteness >= 75 ? 'GOOD' :
                           averageCompleteness >= 60 ? 'MODERATE' : 'POOR';

    const addressAnalysis = analysisResults.addressAnalysis;
    const addressComplianceRate = addressAnalysis ?
        (100 - addressAnalysis.addressDistribution.withoutAddressPercentage) : 0;

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <title>KYC Executive Summary Report</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 40px;
                background: #f8f9fa;
                color: #333;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
                border-bottom: 3px solid #1976d2;
                padding-bottom: 20px;
            }
            .header h1 {
                color: #1976d2;
                margin: 0;
                font-size: 2.5em;
                font-weight: 300;
            }
            .header p {
                color: #666;
                margin: 10px 0 0 0;
                font-size: 1.1em;
            }
            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }
            .metric-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 25px;
                border-radius: 10px;
                text-align: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .metric-card.success { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
            .metric-card.warning { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
            .metric-card.danger { background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }
            .metric-value {
                font-size: 2.5em;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .metric-label {
                font-size: 1.1em;
                opacity: 0.9;
            }
            .section {
                margin: 40px 0;
                padding: 25px;
                background: #f8f9fa;
                border-radius: 8px;
                border-left: 5px solid #1976d2;
            }
            .section h2 {
                color: #1976d2;
                margin-top: 0;
                font-size: 1.8em;
                font-weight: 400;
            }
            .risk-item {
                background: white;
                padding: 15px;
                margin: 10px 0;
                border-radius: 5px;
                border-left: 4px solid #f44336;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .risk-item.medium { border-left-color: #ff9800; }
            .risk-item.low { border-left-color: #4CAF50; }
            .compliance-badge {
                display: inline-block;
                padding: 8px 16px;
                border-radius: 20px;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 0.9em;
            }
            .compliance-excellent { background: #e8f5e8; color: #2e7d32; }
            .compliance-good { background: #e3f2fd; color: #1565c0; }
            .compliance-moderate { background: #fff3e0; color: #ef6c00; }
            .compliance-poor { background: #ffebee; color: #c62828; }
            .recommendations {
                background: #e8f5e8;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
            }
            .recommendations h3 {
                color: #2e7d32;
                margin-top: 0;
            }
            .recommendations ul {
                margin: 0;
                padding-left: 20px;
            }
            .recommendations li {
                margin: 8px 0;
                line-height: 1.5;
            }
            @media print {
                body { background: white; }
                .container { box-shadow: none; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>KYC Executive Summary</h1>
                <p>Generated on ${currentDate} at ${currentTime}</p>
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #1976d2;">
                    <strong>Analysis Method:</strong> Enhanced Validation Applied<br>
                    <em>Address fields (ADD01, OFFICE_ADDRESS, HOME_ADDRESS, MAILING_ADDRESS) use group validation. If ANY address field contains data, ALL address fields are counted as complete for AML compliance.</em><br>
                    <em>Special Field Rules: NO_OF_PTO and NO_OF_SIGNATURE fields with value "0" are treated as BLANK/MISSING for compliance purposes.</em><br>
                    <em>DEFAULT Field Rule: DEFAULT field is only considered BLANK if ALL other fields in the record are blank (reverse validation).</em>
                </div>
            </div>

            <div class="metrics-grid">
                <div class="metric-card ${complianceStatus === 'EXCELLENT' ? 'success' : complianceStatus === 'GOOD' ? '' : complianceStatus === 'MODERATE' ? 'warning' : 'danger'}">
                    <div class="metric-value">${averageCompleteness}%</div>
                    <div class="metric-label">Overall Completion Rate</div>
                </div>

                <div class="metric-card">
                    <div class="metric-value">${analysisResults.totalRecords.toLocaleString()}</div>
                    <div class="metric-label">Total Records</div>
                </div>

                <div class="metric-card ${highRiskFields.length === 0 ? 'success' : 'danger'}">
                    <div class="metric-value">${highRiskFields.length}</div>
                    <div class="metric-label">High Risk Fields</div>
                </div>

                ${addressAnalysis ? `
                <div class="metric-card ${addressComplianceRate >= 95 ? 'success' : addressComplianceRate >= 85 ? 'warning' : 'danger'}">
                    <div class="metric-value">${addressComplianceRate.toFixed(1)}%</div>
                    <div class="metric-label">Address Compliance</div>
                </div>
                ` : ''}
            </div>

            <div class="section">
                <h2>🎯 Compliance Status</h2>
                <p>Overall AML/KYC Compliance Rating:
                    <span class="compliance-badge compliance-${complianceStatus.toLowerCase()}">${complianceStatus}</span>
                </p>
                <p><strong>Complete Profiles:</strong> ${analysisResults.completeProfiles.toLocaleString()} of ${analysisResults.totalRecords.toLocaleString()}</p>
                <p><strong>Profiles Requiring Attention:</strong> ${analysisResults.incompleteProfiles.toLocaleString()}</p>
            </div>

            ${highRiskFields.length > 0 ? `
            <div class="section">
                <h2>🚨 Critical Issues</h2>
                <p><strong>${highRiskFields.length}</strong> fields require immediate attention:</p>
                ${highRiskFields.map(([field, analysis]) => `
                    <div class="risk-item">
                        <strong>${field}</strong>: ${analysis.blankPercentage}% missing data
                        <br><small>Affects ${analysis.blankCount.toLocaleString()} records</small>
                    </div>
                `).join('')}
            </div>
            ` : ''}

            <div class="section">
                <h2>📋 Critical AML Fields Performance</h2>
                ${criticalFieldsAnalysis.map(analysis => `
                    <div class="risk-item ${analysis.riskLevel.toLowerCase()}">
                        <strong>${analysis.field}</strong>: ${analysis.blankPercentage}% missing
                        <span style="float: right; font-weight: bold; color: ${
                            analysis.riskLevel === 'HIGH' ? '#c62828' :
                            analysis.riskLevel === 'MEDIUM' ? '#ef6c00' : '#2e7d32'
                        };">${analysis.riskLevel} RISK</span>
                    </div>
                `).join('')}
            </div>

            ${addressAnalysis ? `
            <div class="section">
                <h2>🏠 Address Validation Summary</h2>
                <p><strong>Address Compliance Rate:</strong> ${addressComplianceRate.toFixed(1)}%</p>
                <p><strong>Records with Valid Address:</strong> ${addressAnalysis.addressDistribution.withAddress.toLocaleString()}</p>
                <p><strong>Records Missing Address:</strong> ${addressAnalysis.addressDistribution.withoutAddress.toLocaleString()}</p>

                <h3>Address Field Usage:</h3>
                ${Object.entries(addressAnalysis.addressFieldStats)
                    .sort((a, b) => b[1].populatedPercentage - a[1].populatedPercentage)
                    .map(([field, stats]) => {
                        const fieldName = field.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                        return `<p><strong>${fieldName}:</strong> ${stats.populatedPercentage}% (${stats.populatedCount.toLocaleString()} records)</p>`;
                    }).join('')}
            </div>
            ` : ''}

            <div class="recommendations">
                <h3>📈 Key Recommendations</h3>
                <ul>
                    ${highRiskFields.length > 0 ?
                        '<li><strong>Immediate Action Required:</strong> Address high-risk fields with missing data rates above 10%</li>' :
                        '<li><strong>Maintain Standards:</strong> Continue current data collection practices</li>'
                    }
                    ${averageCompleteness < 90 ?
                        '<li><strong>Process Improvement:</strong> Review customer onboarding procedures to improve data completeness</li>' : ''
                    }
                    ${addressAnalysis && addressComplianceRate < 95 ?
                        '<li><strong>Address Validation:</strong> Implement enhanced address collection and validation processes</li>' : ''
                    }
                    <li><strong>Automation:</strong> Consider implementing automated follow-up for incomplete profiles</li>
                    <li><strong>Regular Monitoring:</strong> Schedule monthly compliance reviews to maintain data quality</li>
                    <li><strong>Staff Training:</strong> Ensure data entry staff understand AML/KYC requirements</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
                <p>This report was generated automatically by the KYC Data Analysis Tool</p>
                <p>For detailed analysis, please refer to the complete data analysis report</p>
            </div>
        </div>
    </body>
    </html>
    `;
}

function recalculateWithNewAddressCriteria() {
    if (!currentData || currentData.length === 0) {
        showErrorMessage('No dataset is currently loaded. Please upload a CSV or Excel file first to perform AML Risk Assessment with enhanced address validation.');
        return;
    }

    updateProgressIndicator('🔄 Recalculating AML Risk Assessment with Enhanced Address Validation...', 0);
    console.log('Recalculating analysis with new address validation criteria...');

    // Clear existing results
    analysisResults = {};

    // Reset charts safely
    try {
        if (missingDataChart && typeof missingDataChart.destroy === 'function') {
            missingDataChart.destroy();
        }
        missingDataChart = null;
    } catch (e) {
        console.log('Error destroying missing data chart:', e);
    }

    try {
        if (completenessChart && typeof completenessChart.destroy === 'function') {
            completenessChart.destroy();
        }
        completenessChart = null;
    } catch (e) {
        console.log('Error destroying completeness chart:', e);
    }

    try {
        if (window.addressChart && typeof window.addressChart.destroy === 'function') {
            window.addressChart.destroy();
        }
        window.addressChart = null;
    } catch (e) {
        console.log('Error destroying address chart:', e);
    }

    // Clear existing displays
    const analysisSection = document.getElementById('analysisSection');
    if (analysisSection) {
        analysisSection.style.display = 'none';
    }

    const riskGrid = document.getElementById('riskGrid');
    if (riskGrid) {
        riskGrid.innerHTML = '';
    }

    // Update progress
    updateProgressIndicator('🔄 Applying new address group validation logic...', 10);

    // Start fresh analysis with new criteria
    setTimeout(() => {
        updateProgressIndicator('🔄 Processing records with enhanced address validation...', 20);
        analyzeData(currentData);
    }, 100);
}

// Function to check if recalculation is needed and prompt user
function checkAndPromptRecalculation() {
    if (currentData && currentData.length > 0 && Object.keys(analysisResults).length > 0) {
        const recalcButton = document.querySelector('.card-actions .btn-primary');
        if (recalcButton) {
            recalcButton.style.animation = 'pulse 2s infinite';
            recalcButton.title = 'Click to recalculate with enhanced address validation criteria';
        }

        // Show notification
        showSuccessMessage('Dataset loaded! Click "Recalculate Analysis" to apply the new enhanced address validation criteria.');
    }
}

function setupTableControls() {
    // Search functionality
    const searchInput = document.getElementById('tableSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            filterTable(e.target.value);
        });
    }

    // Filter functionality
    const filterSelect = document.getElementById('riskFilter');
    if (filterSelect) {
        filterSelect.addEventListener('change', function(e) {
            filterByRisk(e.target.value);
        });
    }

    // Items per page
    const itemsSelect = document.getElementById('itemsPerPage');
    if (itemsSelect) {
        itemsSelect.addEventListener('change', function(e) {
            itemsPerPage = parseInt(e.target.value);
            currentPage = 1;
            updateTable();
        });
    }
}

function filterTable(searchTerm) {
    if (!analysisResults.fieldAnalysis) return;

    const allFields = Object.entries(analysisResults.fieldAnalysis);

    if (searchTerm.trim() === '') {
        filteredData = allFields;
    } else {
        filteredData = allFields.filter(([field, analysis]) =>
            field.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }

    currentPage = 1;
    updateTable();
}

function filterByRisk(riskLevel) {
    if (!analysisResults.fieldAnalysis) return;

    const allFields = Object.entries(analysisResults.fieldAnalysis);

    if (riskLevel === 'ALL') {
        filteredData = allFields;
    } else {
        filteredData = allFields.filter(([field, analysis]) =>
            analysis.riskLevel === riskLevel
        );
    }

    currentPage = 1;
    updateTable();
}

function sortTable(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    filteredData.sort((a, b) => {
        let aVal, bVal;

        switch (column) {
            case 'field':
                aVal = a[0];
                bVal = b[0];
                break;
            case 'blank':
                aVal = a[1].blankCount;
                bVal = b[1].blankCount;
                break;
            case 'percentage':
                aVal = a[1].blankPercentage;
                bVal = b[1].blankPercentage;
                break;
            case 'risk':
                const riskOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
                aVal = riskOrder[a[1].riskLevel];
                bVal = riskOrder[b[1].riskLevel];
                break;
            default:
                return 0;
        }

        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }

        if (sortDirection === 'asc') {
            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        } else {
            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
        }
    });

    updateTable();
    updateSortIndicators();
}

function updateSortIndicators() {
    // Remove all sort indicators
    document.querySelectorAll('.sortable i').forEach(icon => {
        icon.className = 'fas fa-sort';
    });

    // Add current sort indicator
    if (sortColumn) {
        const header = document.querySelector(`[onclick="sortTable('${sortColumn}')"] i`);
        if (header) {
            header.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        }
    }
}

function updateTable() {
    if (!filteredData.length) return;

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    const tbody = document.getElementById('fieldAnalysisBody');
    tbody.innerHTML = '';

    pageData.forEach(([field, analysis]) => {
        const row = document.createElement('tr');
        row.className = `risk-${analysis.riskLevel.toLowerCase()}`;

        row.innerHTML = `
            <td><strong>${field}</strong></td>
            <td>${analysis.totalRecords.toLocaleString()}</td>
            <td>${analysis.blankCount.toLocaleString()}</td>
            <td>${analysis.blankPercentage}%</td>
            <td><span class="risk-badge risk-${analysis.riskLevel.toLowerCase()}">${analysis.riskLevel}</span></td>
        `;

        tbody.appendChild(row);
    });

    updatePagination();
}

function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredData.length);

    // Update pagination info with null checks
    const paginationStartEl = document.getElementById('paginationStart');
    const paginationEndEl = document.getElementById('paginationEnd');
    const paginationTotalEl = document.getElementById('paginationTotal');

    if (paginationStartEl) paginationStartEl.textContent = startItem;
    if (paginationEndEl) paginationEndEl.textContent = endItem;
    if (paginationTotalEl) paginationTotalEl.textContent = filteredData.length;

    // Update page numbers
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';

    // Previous button
    const prevBtn = document.createElement('button');
    prevBtn.className = 'page-number';
    prevBtn.textContent = '‹';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => changePage(currentPage - 1);
    pageNumbers.appendChild(prevBtn);

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `page-number ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.onclick = () => changePage(i);
        pageNumbers.appendChild(pageBtn);
    }

    // Next button
    const nextBtn = document.createElement('button');
    nextBtn.className = 'page-number';
    nextBtn.textContent = '›';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => changePage(currentPage + 1);
    pageNumbers.appendChild(nextBtn);
}

function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        updateTable();
    }
}

// Fallback functions for main thread processing
function processCSVFallback(csvText, fileName) {
    try {
        console.log('Starting CSV processing...');
        updateProgressIndicator('parse', 0, 'Parsing CSV data...');

        const lines = csvText.split('\n').filter(line => line.trim()); // Remove empty lines
        console.log(`Found ${lines.length} lines in CSV`);

        if (lines.length === 0) {
            throw new Error('CSV file appears to be empty');
        }

        const headers = parseCSVLine(lines[0]);
        console.log('Headers:', headers);

        if (!headers || headers.length === 0) {
            throw new Error('No headers found in CSV file');
        }

        const data = [];

        // Process all data at once for smaller files, or in chunks for larger files
        if (lines.length < 1000) {
            // Process small files immediately
            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim()) {
                    const values = parseCSVLine(lines[i]);
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header.trim().replace(/"/g, '')] = values[index] || '';
                    });
                    data.push(row);
                }
            }

            console.log(`Processed ${data.length} rows immediately`);
            updateProgressIndicator('parse', 100, 'CSV parsing complete');
            setTimeout(() => analyzeDataFallback(data), 100);

        } else {
            // Process large files in chunks
            let currentIndex = 1;

            function processChunk() {
                const chunkSize = 500;
                const endIndex = Math.min(currentIndex + chunkSize, lines.length);

                console.log(`Processing chunk: ${currentIndex} to ${endIndex}`);

                for (let i = currentIndex; i < endIndex; i++) {
                    if (lines[i].trim()) {
                        const values = parseCSVLine(lines[i]);
                        const row = {};
                        headers.forEach((header, index) => {
                            row[header.trim().replace(/"/g, '')] = values[index] || '';
                        });
                        data.push(row);
                    }
                }

                currentIndex = endIndex;
                const progress = Math.round((currentIndex / lines.length) * 100);
                updateProgressIndicator('parse', progress, `Parsing row ${currentIndex} of ${lines.length}`);

                if (currentIndex < lines.length) {
                    setTimeout(processChunk, 10);
                } else {
                    console.log(`CSV parsing complete. Processed ${data.length} rows`);
                    updateProgressIndicator('parse', 100, 'CSV parsing complete');
                    setTimeout(() => analyzeDataFallback(data), 100);
                }
            }

            setTimeout(processChunk, 100);
        }

    } catch (error) {
        console.error('CSV processing error:', error);
        hideProgressIndicators();
        showErrorMessage('Error processing CSV file: ' + error.message);
    }
}

function processExcelFallback(arrayBuffer, fileName) {
    try {
        updateProgressIndicator('parse', 0, 'Parsing Excel data...');

        // Check file size and available memory
        const fileSizeMB = arrayBuffer.byteLength / (1024 * 1024);
        logDebug('info', `Processing Excel file: ${fileSizeMB.toFixed(2)}MB`, {
            fileName: fileName,
            fileSizeMB: fileSizeMB.toFixed(2),
            arrayBufferSize: arrayBuffer.byteLength
        });

        setTimeout(() => {
            try {
                updateProgressIndicator('parse', 10, 'Reading Excel file structure...');

                // Use more memory-efficient options for large files
                const readOptions = {
                    type: 'array',
                    cellDates: false,
                    cellNF: false,
                    cellStyles: false,
                    sheetStubs: false,
                    bookDeps: false,
                    bookFiles: false,
                    bookProps: false,
                    bookSheets: false,
                    bookVBA: false
                };

                // For large files, use optimized settings
                if (fileSizeMB > 50) {
                    readOptions.dense = true; // Use dense mode for large files
                    logDebug('info', 'Using optimized dense mode for large Excel file');
                    updateProgressIndicator('parse', 15, 'Using optimized mode for large file...');
                }

                // For very large files (>100MB), use additional optimizations
                if (fileSizeMB > 100) {
                    readOptions.raw = true; // Skip number formatting
                    logDebug('info', 'Using raw mode for very large Excel file (>100MB)');
                }

                logDebug('info', 'Reading Excel workbook with options', readOptions);
                const workbook = XLSX.read(arrayBuffer, readOptions);
                updateProgressIndicator('parse', 40, 'Excel workbook loaded successfully');

                logDebug('info', 'Excel workbook structure', {
                    sheetNames: workbook.SheetNames,
                    sheetCount: workbook.SheetNames ? workbook.SheetNames.length : 0,
                    workbookProps: Object.keys(workbook)
                });

                if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                    throw new Error('No worksheets found in Excel file');
                }

                const firstSheetName = workbook.SheetNames[0];
                logDebug('info', `Accessing worksheet: "${firstSheetName}"`);

                const worksheet = workbook.Sheets[firstSheetName];

                if (!worksheet) {
                    logDebug('error', 'Worksheet access failed', {
                        sheetName: firstSheetName,
                        availableSheets: workbook.SheetNames,
                        sheetsObject: Object.keys(workbook.Sheets || {})
                    });
                    throw new Error(`Unable to read worksheet "${firstSheetName}". Available sheets: ${workbook.SheetNames.join(', ')}`);
                }

                // Check worksheet structure
                const range = worksheet['!ref'];
                const worksheetKeys = Object.keys(worksheet);
                logDebug('info', 'Worksheet details', {
                    range: range,
                    keyCount: worksheetKeys.length,
                    sampleKeys: worksheetKeys.slice(0, 10)
                });

                if (!range) {
                    throw new Error('Worksheet appears to be empty (no cell range found)');
                }

                updateProgressIndicator('parse', 60, 'Converting Excel data to JSON...');

                setTimeout(() => {
                    try {
                        // Convert with memory-efficient options
                        const jsonOptions = {
                            header: 1,
                            defval: '',
                            blankrows: false
                        };

                        logDebug('info', 'Converting worksheet to JSON with options', jsonOptions);

                        const rawData = XLSX.utils.sheet_to_json(worksheet, jsonOptions);

                        logDebug('info', 'JSON conversion complete', {
                            rowCount: rawData ? rawData.length : 0,
                            firstRowSample: rawData && rawData.length > 0 ? rawData[0] : null
                        });

                        if (!rawData || rawData.length === 0) {
                            throw new Error('No data found in Excel worksheet - the worksheet may be empty or contain only formatting');
                        }

                        if (rawData.length < 2) {
                            throw new Error('Excel file must contain at least a header row and one data row');
                        }

                        updateProgressIndicator('parse', 80, 'Processing Excel data structure...');

                        // Convert array format to object format with chunked processing for large datasets
                        const headers = rawData[0];
                        const data = [];
                        const totalRows = rawData.length - 1; // Exclude header row

                        logDebug('info', `Converting ${totalRows.toLocaleString()} Excel rows to object format`, {
                            totalRows: totalRows,
                            headers: headers.length
                        });

                        // Process in chunks to prevent UI blocking for large datasets
                        const chunkSize = Math.min(10000, Math.max(1000, Math.floor(50000 / headers.length)));
                        let processedRows = 0;

                        function processChunk() {
                            const startIdx = processedRows + 1; // +1 to skip header
                            const endIdx = Math.min(startIdx + chunkSize, rawData.length);

                            for (let i = startIdx; i < endIdx; i++) {
                                const row = {};
                                headers.forEach((header, index) => {
                                    row[header] = rawData[i][index] || '';
                                });
                                data.push(row);
                                processedRows++;
                            }

                            const progress = Math.min(99, 80 + (processedRows / totalRows) * 19);
                            updateProgressIndicator('parse', progress, `Processed ${processedRows.toLocaleString()} of ${totalRows.toLocaleString()} rows...`);

                            if (processedRows < totalRows) {
                                // Continue processing next chunk
                                setTimeout(processChunk, 10);
                            } else {
                                // Processing complete
                                updateProgressIndicator('parse', 100, 'Excel parsing complete');
                                logDebug('success', `Successfully processed ${data.length.toLocaleString()} rows from Excel file`, {
                                    totalRows: data.length,
                                    processingTime: Date.now() - processingStartTime
                                });

                                // Clear the array buffer and raw data to free memory
                                arrayBuffer = null;
                                rawData.length = 0;

                                // Store data and start analysis
                                currentData = data;
                                const recordCountEl = document.getElementById('recordCount');
                                if (recordCountEl) recordCountEl.textContent = data.length.toLocaleString();

                                setTimeout(() => {
                                    analyzeDataProgressive();
                                }, 100);
                            }
                        }

                        // Start chunked processing
                        processChunk();

                    } catch (conversionError) {
                        logDebug('error', 'Excel conversion error', {
                            error: conversionError.message,
                            stack: conversionError.stack,
                            fileName: fileName,
                            fileSizeMB: fileSizeMB
                        });
                        hideProgressIndicators();
                        showErrorMessageWithDebug(`Error converting Excel data: ${conversionError.message}. Try saving as CSV format for better compatibility.`);
                    }
                }, 100);

            } catch (readError) {
                logDebug('error', 'Excel read error - trying fallback approach', {
                    error: readError.message,
                    stack: readError.stack,
                    fileName: fileName,
                    fileSizeMB: fileSizeMB,
                    arrayBufferSize: arrayBuffer ? arrayBuffer.byteLength : 'null'
                });

                // Try fallback approach with minimal options
                try {
                    logDebug('info', 'Attempting fallback Excel reading with minimal options');
                    updateProgressIndicator('parse', 20, 'Trying alternative Excel reading method...');

                    const fallbackOptions = {
                        type: 'array',
                        cellDates: false,
                        cellStyles: false,
                        raw: false // Try without raw mode first
                    };

                    const workbook = XLSX.read(arrayBuffer, fallbackOptions);

                    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                        throw new Error('No worksheets found in Excel file');
                    }

                    // Try each sheet until we find one with data
                    let worksheet = null;
                    let sheetName = null;

                    for (const name of workbook.SheetNames) {
                        const sheet = workbook.Sheets[name];
                        if (sheet && sheet['!ref']) {
                            worksheet = sheet;
                            sheetName = name;
                            logDebug('info', `Found data in worksheet: "${name}"`);
                            break;
                        }
                    }

                    if (!worksheet) {
                        throw new Error('No worksheets contain data');
                    }

                    updateProgressIndicator('parse', 60, `Processing data from sheet: ${sheetName}`);

                    // Continue with the successful worksheet
                    setTimeout(() => {
                        try {
                            const jsonOptions = {
                                header: 1,
                                defval: '',
                                blankrows: false
                            };

                            const rawData = XLSX.utils.sheet_to_json(worksheet, jsonOptions);

                            if (!rawData || rawData.length < 2) {
                                throw new Error('Worksheet contains insufficient data (need header + data rows)');
                            }

                            logDebug('success', `Fallback method successful - found ${rawData.length} rows`);

                            // Continue with normal processing
                            updateProgressIndicator('parse', 80, 'Processing Excel data structure...');

                            // Convert array format to object format with chunked processing
                            const headers = rawData[0];
                            const data = [];
                            const totalRows = rawData.length - 1;

                            // Process in chunks
                            const chunkSize = Math.min(10000, Math.max(1000, Math.floor(50000 / headers.length)));
                            let processedRows = 0;

                            function processChunk() {
                                const startIdx = processedRows + 1;
                                const endIdx = Math.min(startIdx + chunkSize, rawData.length);

                                for (let i = startIdx; i < endIdx; i++) {
                                    const row = {};
                                    headers.forEach((header, index) => {
                                        row[header] = rawData[i][index] || '';
                                    });
                                    data.push(row);
                                    processedRows++;
                                }

                                const progress = Math.min(99, 80 + (processedRows / totalRows) * 19);
                                updateProgressIndicator('parse', progress, `Processed ${processedRows.toLocaleString()} of ${totalRows.toLocaleString()} rows...`);

                                if (processedRows < totalRows) {
                                    setTimeout(processChunk, 10);
                                } else {
                                    updateProgressIndicator('parse', 100, 'Excel parsing complete');
                                    logDebug('success', `Successfully processed ${data.length.toLocaleString()} rows from Excel file`);

                                    arrayBuffer = null;
                                    rawData.length = 0;
                                    currentData = data;

                                    const recordCountEl = document.getElementById('recordCount');
                                    if (recordCountEl) recordCountEl.textContent = data.length.toLocaleString();

                                    setTimeout(() => analyzeDataProgressive(), 100);
                                }
                            }

                            processChunk();

                        } catch (fallbackError) {
                            logDebug('error', 'Fallback processing failed', {
                                error: fallbackError.message,
                                stack: fallbackError.stack
                            });
                            hideProgressIndicators();
                            showErrorMessageWithDebug(`Error processing Excel data: ${fallbackError.message}`);
                        }
                    }, 100);

                } catch (fallbackReadError) {
                    logDebug('error', 'Fallback Excel reading also failed', {
                        error: fallbackReadError.message,
                        originalError: readError.message
                    });
                    hideProgressIndicators();

                    let errorMessage = `Error reading Excel file: ${readError.message}`;

                    if (readError.message.includes('Invalid array length') ||
                        readError.message.includes('out of memory')) {
                        errorMessage = `File too large for browser memory (${fileSizeMB.toFixed(1)}MB). Please try:\n• Converting to CSV format\n• Using a smaller file\n• Processing in smaller chunks`;
                    } else if (readError.message.includes('Unable to read worksheet')) {
                        errorMessage = `Excel file structure issue: ${readError.message}\n\nPossible solutions:\n• Save as .xlsx format (not .xls)\n• Remove password protection\n• Convert to CSV format\n• Check for corrupted data`;
                    }

                    showErrorMessageWithDebug(errorMessage);
                }
            }
        }, 100);

    } catch (error) {
        logDebug('error', 'Excel processing error', {
            error: error.message,
            stack: error.stack,
            fileName: fileName,
            arrayBufferSize: arrayBuffer ? arrayBuffer.byteLength : 'null'
        });
        hideProgressIndicators();
        showErrorMessageWithDebug('Error processing Excel file: ' + error.message);
    }
}

function analyzeDataFallback(data) {
    try {
        console.log('Starting data analysis...', data.length, 'records');
        updateProgressIndicator('validate', 0, 'Validating data...');

        if (!data || data.length === 0) {
            throw new Error('No data found in file');
        }

        console.log('Data validation passed');
        updateProgressIndicator('validate', 100, 'Data validation complete');
        updateProgressIndicator('analyze', 0, 'Starting analysis...');

        const availableFields = Object.keys(data[0]);
        console.log('Available fields:', availableFields);

        const fieldAnalysis = {};

        // Initialize field analysis
        availableFields.forEach(field => {
            fieldAnalysis[field] = {
                totalRecords: data.length,
                blankCount: 0,
                blankPercentage: 0,
                riskLevel: 'LOW'
            };
        });

        // Initialize address group analysis
        fieldAnalysis['ADDRESS_GROUP'] = {
            totalRecords: data.length,
            blankCount: 0,
            blankPercentage: 0,
            riskLevel: 'LOW'
        };

        // Initialize address field analysis
        const addressAnalysis = {
            totalRecords: data.length,
            addressFieldStats: {},
            addressDistribution: {},
            addressCompletionPatterns: {}
        };

        // Initialize stats for each address field
        addressFields.forEach(field => {
            addressAnalysis.addressFieldStats[field] = {
                populatedCount: 0,
                populatedPercentage: 0
            };
        });

        const profileCompleteness = [];

        // For smaller datasets, process immediately
        if (data.length < 1000) {
            console.log('Processing small dataset immediately');

            data.forEach((row, index) => {
                availableFields.forEach(field => {
                    if (isFieldValueBlank(field, row[field], row, availableFields)) {
                        fieldAnalysis[field].blankCount++;
                    }
                });

                // Calculate profile completeness with special field validation
                const filledFields = availableFields.filter(field =>
                    !isFieldValueBlank(field, row[field], row, availableFields)
                ).length;

                profileCompleteness.push((filledFields / availableFields.length) * 100);
            });

            console.log('Small dataset analysis complete');
            finalizeAnalysis();

        } else {
            // Process large datasets in chunks
            console.log('Processing large dataset in chunks');
            let currentIndex = 0;

            function analyzeChunk() {
                const chunkSize = 1000;
                const endIndex = Math.min(currentIndex + chunkSize, data.length);

                console.log(`Analyzing chunk: ${currentIndex} to ${endIndex}`);

                for (let i = currentIndex; i < endIndex; i++) {
                    const row = data[i];

                    // First, analyze address group requirement
                    let hasAnyAddress = false;
                    const recordAddressPattern = [];

                    addressFields.forEach(field => {
                        const hasValue = !isFieldValueBlank(field, row[field], row, availableFields);

                        if (hasValue) {
                            hasAnyAddress = true;
                            addressAnalysis.addressFieldStats[field].populatedCount++;
                            recordAddressPattern.push(field);
                        }
                    });

                    // Analyze each field with address group validation and special field rules
                    availableFields.forEach(field => {
                        const isEmpty = isFieldValueBlank(field, row[field], row, availableFields);

                        // Apply address group validation
                        if (addressFields.includes(field)) {
                            // For address fields, only count as blank if NO address exists
                            if (!hasAnyAddress) {
                                fieldAnalysis[field].blankCount++;
                            }
                        } else {
                            // For non-address fields, count normally with special validation
                            if (isEmpty) {
                                fieldAnalysis[field].blankCount++;
                            }
                        }
                    });

                    // Mark address group as complete if any address field is populated
                    if (!hasAnyAddress) {
                        fieldAnalysis['ADDRESS_GROUP'].blankCount++;
                    }

                    // Track address completion patterns
                    const patternKey = recordAddressPattern.sort().join(',') || 'NO_ADDRESS';
                    addressAnalysis.addressCompletionPatterns[patternKey] =
                        (addressAnalysis.addressCompletionPatterns[patternKey] || 0) + 1;

                    // Calculate profile completeness using address group validation and special field rules
                    const filledFields = availableFields.filter(field =>
                        !isFieldValueBlank(field, row[field], row, availableFields)
                    ).length;

                    // Apply address group validation to completeness calculation
                    let adjustedFilledFields = filledFields;

                    // If any address field is populated, count all address fields as filled
                    if (hasAnyAddress) {
                        // Add missing address fields to the filled count
                        addressFields.forEach(field => {
                            if (availableFields.includes(field) && isFieldValueBlank(field, row[field], row, availableFields)) {
                                adjustedFilledFields++;
                            }
                        });
                    }

                    profileCompleteness.push((adjustedFilledFields / availableFields.length) * 100);
                }

                currentIndex = endIndex;
                const progress = Math.round((currentIndex / data.length) * 100);
                updateProgressIndicator('analyze', progress, `Analyzed ${currentIndex} of ${data.length} records`);

                if (currentIndex < data.length) {
                    setTimeout(analyzeChunk, 10);
                } else {
                    console.log('Large dataset analysis complete');
                    setTimeout(finalizeAnalysis, 100);
                }
            }

            setTimeout(analyzeChunk, 100);
        }

        function finalizeAnalysis() {
            console.log('Finalizing analysis...');

            // Calculate final statistics
            availableFields.forEach(field => {
                const analysis = fieldAnalysis[field];
                analysis.blankPercentage = parseFloat(((analysis.blankCount / data.length) * 100).toFixed(2));
                analysis.riskLevel = getRiskLevelFallback(field, analysis.blankPercentage);
            });

            // Calculate address group statistics
            const addressGroupData = fieldAnalysis['ADDRESS_GROUP'];
            addressGroupData.blankPercentage = parseFloat(((addressGroupData.blankCount / data.length) * 100).toFixed(2));
            addressGroupData.riskLevel = getRiskLevelFallback('ADDRESS_GROUP', addressGroupData.blankPercentage);

            // Calculate address field percentages
            addressFields.forEach(field => {
                const stats = addressAnalysis.addressFieldStats[field];
                stats.populatedPercentage = parseFloat(((stats.populatedCount / data.length) * 100).toFixed(2));
            });

            // Calculate address distribution
            const totalWithAddress = data.length - addressGroupData.blankCount;
            addressAnalysis.addressDistribution = {
                withAddress: totalWithAddress,
                withAddressPercentage: parseFloat(((totalWithAddress / data.length) * 100).toFixed(2)),
                withoutAddress: addressGroupData.blankCount,
                withoutAddressPercentage: addressGroupData.blankPercentage
            };

            const completeProfiles = profileCompleteness.filter(p => p === 100).length;
            const incompleteProfiles = data.length - completeProfiles;
            const completionRate = parseFloat(((completeProfiles / data.length) * 100).toFixed(1));

            updateProgressIndicator('analyze', 100, 'Analysis complete');

            // Handle results
            const results = {
                fieldAnalysis,
                profileCompleteness,
                totalRecords: data.length,
                completeProfiles,
                incompleteProfiles,
                completionRate,
                availableFields,
                addressAnalysis
            };

            console.log('Analysis results:', results);
            setTimeout(() => handleAnalysisComplete(results), 100);
        }

    } catch (error) {
        console.error('Analysis error:', error);
        hideProgressIndicators();
        showErrorMessage('Error analyzing data: ' + error.message);
    }
}

function getRiskLevelFallback(field, blankPercentage) {
    const isCritical = criticalFields.includes(field);

    if (isCritical) {
        if (blankPercentage > 10) return 'HIGH';
        if (blankPercentage > 5) return 'MEDIUM';
        return 'LOW';
    } else {
        if (blankPercentage > 25) return 'HIGH';
        if (blankPercentage > 15) return 'MEDIUM';
        return 'LOW';
    }
}

function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim().replace(/^"|"$/g, ''));
            current = '';
        } else {
            current += char;
        }
    }
    result.push(current.trim().replace(/^"|"$/g, ''));
    return result;
}

// Processing timeout functions

function startProcessingTimeout() {
    // Clear any existing timeouts
    if (processingTimeout) {
        clearTimeout(processingTimeout);
    }
    if (progressHeartbeat) {
        clearInterval(progressHeartbeat);
    }

    console.log('Starting processing timeout (10 minutes) and heartbeat monitor');
    lastProgressTime = Date.now();

    // Set 10 minute timeout for large files
    processingTimeout = setTimeout(() => {
        console.error('Processing timeout reached');
        hideProgressIndicators();
        showErrorMessage('Processing timeout after 10 minutes. The file may be extremely large or there may be a processing issue. Please try with a smaller file or check the browser console for errors.');
    }, 600000); // 10 minutes (600 seconds)

    // Heartbeat to detect stuck processing (check every 30 seconds)
    progressHeartbeat = setInterval(() => {
        const timeSinceLastProgress = Date.now() - lastProgressTime;
        if (timeSinceLastProgress > 120000) { // 2 minutes without progress
            logDebug('error', 'Processing appears stuck - no progress for 2 minutes', {
                timeSinceLastProgress: timeSinceLastProgress,
                lastProgressTime: lastProgressTime,
                currentTime: Date.now()
            });
            clearInterval(progressHeartbeat);
            clearTimeout(processingTimeout);
            hideProgressIndicators();
            showErrorMessageWithDebug('Processing appears to be stuck. This may indicate a browser memory issue with very large files. Please try with a smaller file or refresh the page and try again.');
        } else {
            logDebug('info', `Processing heartbeat: ${Math.round(timeSinceLastProgress/1000)}s since last progress`);
            // Update debug stats during processing
            if (debugConsoleVisible) {
                updateDebugStats();
            }
        }
    }, 30000); // Check every 30 seconds
}

function clearProcessingTimeout() {
    if (processingTimeout) {
        console.log('Clearing processing timeout');
        clearTimeout(processingTimeout);
        processingTimeout = null;
    }
    if (progressHeartbeat) {
        clearInterval(progressHeartbeat);
        progressHeartbeat = null;
        console.log('Progress heartbeat cleared');
    }
}

// Debug Console Functions
function toggleDebugConsole() {
    const debugSection = document.getElementById('debugSection');
    const toggleBtn = document.getElementById('toggleDebug');

    debugConsoleVisible = !debugConsoleVisible;

    if (debugConsoleVisible) {
        debugSection.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-bug"></i> Hide Debug Console';
        logDebug('info', 'Debug console opened');
        updateDebugStats();
    } else {
        debugSection.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-bug"></i> Show Debug Console';
    }
}

function logDebug(level, message, data = null) {
    const debugLog = document.getElementById('debugLog');
    if (!debugLog) return;

    const timestamp = new Date().toLocaleTimeString();
    const entry = document.createElement('div');
    entry.className = 'debug-entry';

    let logMessage = `[${timestamp}] ${message}`;
    if (data) {
        logMessage += `\nData: ${JSON.stringify(data, null, 2)}`;
    }

    entry.innerHTML = `
        <span class="debug-timestamp">${timestamp}</span>
        <span class="debug-level-${level}">[${level.toUpperCase()}]</span>
        <span>${message}</span>
        ${data ? `<pre style="margin-top: 5px; font-size: 11px;">${JSON.stringify(data, null, 2)}</pre>` : ''}
    `;

    debugLog.appendChild(entry);
    debugLog.scrollTop = debugLog.scrollHeight;

    // Also log to browser console
    console.log(`[DEBUG ${level.toUpperCase()}] ${message}`, data || '');
}

function clearDebugLog() {
    const debugLog = document.getElementById('debugLog');
    if (debugLog) {
        debugLog.innerHTML = '';
        logDebug('info', 'Debug log cleared');
    }
}

function exportDebugLog() {
    const debugLog = document.getElementById('debugLog');
    if (!debugLog) return;

    const logContent = debugLog.textContent || debugLog.innerText;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    downloadFile(logContent, `debug-log-${timestamp}.txt`, 'text/plain');
}

function updateDebugStats() {
    const memoryUsage = document.getElementById('memoryUsage');
    const processingTime = document.getElementById('processingTime');
    const processingSpeed = document.getElementById('processingSpeed');

    if (memoryUsage) {
        if (performance.memory) {
            const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
            memoryUsage.textContent = `${used}MB / ${total}MB`;
        } else {
            memoryUsage.textContent = 'Not available';
        }
    }

    if (processingTime && processingStartTime) {
        const elapsed = Math.round((Date.now() - processingStartTime) / 1000);
        processingTime.textContent = `${elapsed}s`;
    }

    if (processingSpeed && currentData.length > 0 && processingStartTime) {
        const elapsed = (Date.now() - processingStartTime) / 1000;
        const speed = Math.round(currentData.length / elapsed);
        processingSpeed.textContent = `${speed}`;
    }
}

function pauseProcessing() {
    processingPaused = !processingPaused;
    const pauseBtn = document.getElementById('pauseProcessing');

    if (processingPaused) {
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> Resume Processing';
        logDebug('warn', 'Processing paused by user');
    } else {
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Pause Processing';
        logDebug('info', 'Processing resumed by user');
    }
}

// Enhanced error handling with debug logging
function showErrorMessageWithDebug(message, error = null) {
    logDebug('error', message, error);
    showErrorMessage(message);
}

// Override console methods to capture errors in debug console
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

console.error = function(...args) {
    originalConsoleError.apply(console, args);
    if (debugConsoleVisible) {
        logDebug('error', args.join(' '));
    }
};

console.warn = function(...args) {
    originalConsoleWarn.apply(console, args);
    if (debugConsoleVisible) {
        logDebug('warn', args.join(' '));
    }
};

console.log = function(...args) {
    originalConsoleLog.apply(console, args);
    if (debugConsoleVisible) {
        logDebug('info', args.join(' '));
    }
};

// Capture unhandled errors
window.addEventListener('error', function(event) {
    const errorMsg = `${event.message} at ${event.filename}:${event.lineno}:${event.colno}`;
    logDebug('error', 'Unhandled JavaScript Error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error ? event.error.stack : 'No stack trace available'
    });
});

// Capture unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
    logDebug('error', 'Unhandled Promise Rejection', {
        reason: event.reason,
        promise: event.promise
    });
});
